# ruff: noqa: I001, E402
"""Workflow orchestrator for executing multi-step workflows with agents."""

import asyncio
import logging
import uuid
from typing import Any

import httpx
from a2a.client import A2<PERSON>ardResolver, A2AClient
from a2a.types import (
    JSONRPCErrorResponse,
    Message,
    MessageSendParams,
    SendMessageRequest,
    Task,
    TaskState,
)
from fastapi import HTTPException
from gen_os_am_workflows.api.models.request import (
    OccurrenceCreate,
    StepRunCreate,
    StepRunUpdate,
    WorkflowExecutionCreate,
    WorkflowExecutionUpdate,
)
from gen_os_am_workflows.api.router import (
    create_occurrence,
    create_step_run,
    create_workflow_execution,
    get_step_run,
    get_workflow_execution,
    update_step_run,
    update_workflow_execution,
)
from gen_os_am_workflows.database.models import (
    InteractionKind,
    InteractionKindApproved,
    InteractionKindCancelled,
    InteractionKindEdited,
    InteractionKindResumed,
    InteractionKindSolveManually,
    OccurrenceReasonAssistanceNeeded,
    Occurrence<PERSON><PERSON>on<PERSON>rror,
    OccurrenceReasonPendingApproval,
    OccurrenceReasons,
    Step,
    StepRun,
    StepRunStatusCompleted,
    StepRunStatusRunning,
    StepTypeAction,
    WorkflowExecution,
    WorkflowExecutionStatusError,
    WorkflowExecutionStatusRunning,
)
from sqlalchemy import select

from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.database.session import SessionManager
from gen_os_am_core.settings import Settings
from gen_os_am_core.workflows.a2a_utils import (
    create_message_parts_from_step_run,
    extract_outputs_from_artifacts,
    get_default_block_data,
)
from gen_os_am_core.workflows.models import StepExecutionResponse
from gen_os_am_core.workflows.schemas import InputData
from gen_os_am_core.workflows.state_store import WorkflowStateStore


class WorkflowOrchestrator:
    """Orchestrates workflow execution with agent communication via A2A protocol."""

    def __init__(self, state_store: WorkflowStateStore | None = None):
        """Initialize the orchestrator with settings and state store."""
        self.settings = Settings.get_settings()
        self.logger = logging.getLogger(__name__)

        # Use external state store instead of in-memory dictionaries
        self.state_store = state_store or WorkflowStateStore()

        # Keep active executions in memory for task management within this process
        self.active_executions: dict[uuid.UUID, asyncio.Task] = {}

    async def trigger_workflow(
        self,
        workflow_name: str,
        input_data: list[InputData],
        test_execution: bool = False,
        agent_id: str | None = None,
    ) -> uuid.UUID:
        """Trigger a workflow execution.

        Args:
            workflow_name: Name of the workflow to execute
            input_data: Input data for the workflow to start
            test_execution: Whether to create a test execution
            agent_id: Agent ID for filtering and context

        Returns:
            UUID of the created execution

        Raises:
            ValueError: If workflow not found or invalid

        """
        self.logger.info(
            f"Triggering workflow {workflow_name} for agent {agent_id} "
            f"with input: {input_data}"
        )

        try:
            # Create workflow execution via gen_os_am_workflows
            execution: WorkflowExecution = await self._create_workflow_execution(
                workflow_name=workflow_name,
                test_execution=test_execution,
                agent_id=agent_id,
            )
            execution_id = execution.id

            # Create event for managing execution flow
            await self.state_store.set_event(execution_id)

            # Start background execution task
            task = asyncio.create_task(
                self._execute_workflow(
                    workflow_name=workflow_name,
                    execution_id=execution_id,
                    agent_id=agent_id,
                    triggering_data=input_data,
                )
            )
            self.active_executions[execution_id] = task

            self.logger.info(
                f"Started workflow execution {execution_id} for agent {agent_id}"
            )
            return execution_id

        except HTTPException as e:
            self.logger.error(
                f"Failed to trigger workflow {workflow_name} for agent {agent_id}: {e}"
            )
            raise HTTPException(status_code=e.status_code, detail=e.detail) from e

        except Exception as e:
            self.logger.error(
                f"Failed to trigger workflow {workflow_name} for agent {agent_id}: {e}"
            )
            raise

    async def handle_interaction_notification(
        self,
        execution_id: uuid.UUID,
        step_run_id: uuid.UUID,
        occurrence_id: uuid.UUID,
        interaction_id: uuid.UUID,
        interaction_kind: InteractionKind,
        interaction_data: dict[str, Any],
        agent_id: str | None = None,
    ) -> None:
        """Handle notification that an interaction has been created/updated.

        This method is called when gen_os_am_workflows creates an interaction
        and the InteractionStateChangeCascade updates the workflow execution status.

        Args:
            execution_id: ID of the workflow execution
            step_run_id: ID of the step run
            occurrence_id: ID of the occurrence
            interaction_id: ID of the interaction
            interaction_kind: Kind of interaction
            interaction_data: Data about the interaction
            agent_id: Agent ID for filtering and context

        """
        self.logger.info(
            f"Received interaction notification for execution {execution_id} "
            f"(agent {agent_id}): {interaction_data}"
        )

        if interaction_kind == InteractionKindApproved:
            # Approved: move to next step in workflow
            await self._resume_execution(execution_id)
        elif interaction_kind in [InteractionKindResumed, InteractionKindSolveManually]:
            # Resumed/SolveManually: user modified step run, resend to agent
            await self._retry_current_step(execution_id, step_run_id, agent_id)
        elif interaction_kind == InteractionKindCancelled:
            # Cancel the workflow execution
            await self._cancel_execution(execution_id)
        elif interaction_kind == InteractionKindEdited:
            # Edited interactions don't change execution flow, just log
            self.logger.info(
                f"Step data edited for execution {execution_id} (agent {agent_id})"
            )

    async def _get_agent_url(self, agent_id: str) -> str:
        """Get the URL of an agent.

        Args:
            agent_id: Agent ID for filtering and context

        Returns:
            URL of the agent

        """
        try:
            async with SessionManager.get_session() as db:
                agent = await db.execute(select(Agent).where(Agent.id == agent_id))
                agent = agent.scalars().first()
                if not agent:
                    raise ValueError(f"Agent {agent_id} not found")
                if not agent.url:
                    raise ValueError(f"Agent {agent_id} has no URL")
                return agent.url
        except Exception as e:
            self.logger.error(f"Failed to get agent URL for agent {agent_id}: {e}")
            raise HTTPException(
                status_code=404, detail=f"Agent {agent_id} not found"
            ) from e

    async def _resume_execution(self, execution_id: uuid.UUID) -> None:
        """Resume a workflow execution after interaction resolution.

        Args:
            execution_id: ID of the execution to resume

        """
        self.logger.info(f"Resuming workflow execution {execution_id}")

        # Signal the execution to continue
        event = await self.state_store.get_event(execution_id)
        if event:
            event.set()
            self.logger.info(f"Signaled execution {execution_id} to continue")
        else:
            self.logger.warning(f"No event found for execution {execution_id}")

    async def _retry_current_step(
        self, execution_id: uuid.UUID, step_run_id: uuid.UUID, agent_id: str
    ) -> None:
        """Retry the current step with modified step run data.

        Used when user has manually modified the step run and we need to resend it
        to the agent for processing.

        Args:
            execution_id: ID of the execution
            step_run_id: ID of the step run to retry
            agent_id: ID of the agent to retry the step run for

        """
        self.logger.info(
            f"Retrying step run {step_run_id} for execution {execution_id}"
        )

        try:
            # Get the updated step run from gen_os_am_workflows
            step_run = await get_step_run(step_run_id)

            # Execute the existing step run using _execute_step method
            result = await self._execute_step(
                workflow_name=step_run.step.workflow.name,
                execution_id=execution_id,
                agent_id=agent_id,
                step_run=step_run,
            )

            # Only resume execution if the step didn't result in a blocking occurrence
            # If it did, _execute_step already handled the waiting, so we don't resume
            if result.reason not in [
                OccurrenceReasonAssistanceNeeded,
                OccurrenceReasonPendingApproval,
                OccurrenceReasonError,
            ]:
                # Signal the execution to continue after successful retry
                await self._resume_execution(execution_id)
            else:
                self.logger.info(
                    f"Step retry resulted in occurrence ({result.reason}), "
                    f"execution {execution_id} will remain paused until interaction"
                )

        except Exception as e:
            self.logger.error(
                f"Failed to retry step run {step_run_id} for execution "
                f"{execution_id}: {e}"
            )
            raise

    async def _cancel_execution(self, execution_id: uuid.UUID) -> None:
        """Cancel a workflow execution.

        Args:
            execution_id: ID of the execution to cancel

        """
        self.logger.info(f"Cancelling workflow execution {execution_id}")

        # Cancel the active task
        if execution_id in self.active_executions:
            task = self.active_executions[execution_id]
            task.cancel()
            del self.active_executions[execution_id]

        # Clean up event
        await self.state_store.clear_event(execution_id)

    async def _execute_workflow(
        self,
        workflow_name: str,
        execution_id: uuid.UUID,
        agent_id: str | None = None,
        triggering_data: list[InputData] | None = None,
    ) -> None:
        """Execute a workflow by processing its steps in sequence.

        Args:
            workflow_name: Name of the workflow to execute
            execution_id: ID of the workflow execution to run
            agent_id: Agent ID for filtering and context
            triggering_data: Input data for the workflow to start

        """
        self.logger.info(
            f"Starting workflow execution {execution_id} for agent {agent_id}"
        )

        try:
            # Get workflow definition and execution details
            execution: WorkflowExecution = await get_workflow_execution(
                workflow_name=workflow_name,
                workflow_execution_id=execution_id,
            )
            workflow = execution.workflow
            steps = workflow.steps

            # Execute each step in sequence
            step_results: dict[str, StepExecutionResponse] = {}
            for step_index, step in enumerate(steps):
                try:
                    if execution.test_execution and step.step_type == StepTypeAction:
                        self.logger.info(
                            f"Skipping action step {step.name} in test execution "
                            f"for agent {agent_id}"
                        )
                        continue

                    self.logger.info(
                        f"Executing step {step_index + 1}/{len(steps)}: {step.name} "
                        f"for agent {agent_id}"
                    )
                    # Execute the step
                    step_result: StepExecutionResponse = await self._execute_step(
                        workflow_name=workflow_name,
                        step=step,
                        previous_results=step_results,
                        execution_id=execution_id,
                        agent_id=agent_id,
                        triggering_data=triggering_data if step_index == 0 else None,
                    )

                    # Check if step resulted in an occurrence that blocks execution
                    if step_result.reason == OccurrenceReasonError:
                        self.logger.info(
                            f"Step {step.name} is blocked for agent {agent_id}, "
                            f"waiting for interaction resolution"
                        )

                        # Wait for interaction to resolve via notification
                        await self._wait_for_interaction_resolution(execution_id)

                        # After interaction resolved, continue with next step
                        self.logger.info(
                            f"Interaction resolved for agent {agent_id}, "
                            f"continuing workflow"
                        )

                    step_results[step.name] = step_result
                    self.logger.info(
                        f"Step {step.name} completed successfully for agent {agent_id}"
                    )

                except Exception as e:
                    self.logger.error(
                        f"Step {step.name} failed for agent {agent_id}: {e}"
                    )

                    # Mark execution as failed
                    update_request = WorkflowExecutionUpdate(
                        status=WorkflowExecutionStatusError
                    )
                    await update_workflow_execution(execution_id, update_request)

                    raise e

            # TODO(Henrique): Check if we need to do this
            # execution should be marked completed when the last step run is completed
            # through state change cascade
            # Mark execution as completed
            # update_request = WorkflowExecutionUpdate(
            #     status=WorkflowExecutionStatusCompleted
            # )
            # await update_workflow_execution(execution_id, update_request)

            self.logger.info(
                f"Workflow execution {execution_id} completed successfully "
                f"for agent {agent_id}"
            )

        except asyncio.CancelledError:
            self.logger.info(
                f"Workflow execution {execution_id} was cancelled for agent {agent_id}"
            )
            raise
        except Exception as e:
            self.logger.error(
                f"Workflow execution {execution_id} failed for agent {agent_id}: {e}"
            )
            raise
        finally:
            # Clean up
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]
            events = await self.state_store.get_events()
            if execution_id in events:
                await self.state_store.clear_event(execution_id)

    async def _execute_step(
        self,
        workflow_name: str,
        execution_id: uuid.UUID,
        agent_id: str | None = None,
        step: Step | None = None,
        step_run: StepRun | None = None,
        previous_results: dict[str, StepExecutionResponse] | None = None,
        triggering_data: list[InputData] | None = None,
    ) -> StepExecutionResponse:
        """Execute a single workflow step (create new or update existing step run).

        Args:
            workflow_name: Name of the workflow to execute
            execution_id: Current execution ID
            agent_id: Agent ID for filtering and context
            step: The workflow step to execute (for new step runs)
            step_run: Existing step run to execute (for retries/updates)
            previous_results: Results from previous steps (for new step runs)
            triggering_data: Input data for the workflow to start (for new step runs)

        Returns:
            StepExecutionResponse containing step execution results

        """
        # Determine if we're creating a new step run or using an existing one
        if step_run is not None:
            # Using existing step run (retry/update scenario)
            step_name = step_run.step.name
            self.logger.info(
                f"Executing existing step run {step_run.id}: {step_name} "
                f"from execution {execution_id} for agent {agent_id}"
            )
        elif step is not None:
            # Creating new step run
            step_name = step.name
            self.logger.info(
                f"Executing step: {step_name} from execution {execution_id} "
                f"for agent {agent_id}"
            )
            try:
                # Create step run
                step_run = await self._create_step_run(
                    workflow_name=workflow_name,
                    step=step,
                    previous_results=previous_results or {},
                    execution_id=execution_id,
                    triggering_data=triggering_data,
                )
            except Exception as e:
                self.logger.error(
                    f"Error creating step run for step {step_name} on execution "
                    f"{execution_id}: {e}"
                )
                raise e
        else:
            raise ValueError("Either 'step' or 'step_run' must be provided")

        try:
            # Retry logic for agent communication
            for attempt in range(self.settings.WORKFLOW_AGENT_RETRY_COUNT):
                try:
                    agent_response = await self._communicate_with_agent(
                        step_run=step_run,
                        agent_id=agent_id,
                        context_id=await self.state_store.get_context(execution_id),
                    )
                    break  # Success, exit retry loop
                except Exception as e:
                    retry_count = attempt + 1
                    self.logger.warning(
                        f"Agent communication attempt {retry_count} failed for agent "
                        f"{agent_id}: {e}"
                    )

                    if retry_count >= self.settings.WORKFLOW_AGENT_RETRY_COUNT:
                        # Create error occurrence
                        await self._create_occurrence(
                            workflow_name=workflow_name,
                            execution_id=execution_id,
                            step_run_id=step_run.id,
                            occurrence_reason=OccurrenceReasonError,
                            message=f"Agent communication failed after {retry_count} "
                            f"attempts",
                        )
                        return StepExecutionResponse(reason=OccurrenceReasonError)

                    # Exponential backoff for retries
                    await asyncio.sleep(2**attempt)

            # Process agent response
            result = await self._process_agent_response(
                workflow_name=workflow_name,
                agent_response=agent_response,
                step_run=step_run,
                execution_id=execution_id,
                agent_id=agent_id,
            )

            # Update context_id if provided in response
            if result.context_id:
                await self.state_store.set_context(execution_id, result.context_id)

            # Update the step run with outputs and status
            await self._update_step_run_with_result(step_run, result)

            # If step is blocked due to assistance needed, wait for human interaction
            if result.reason == OccurrenceReasonAssistanceNeeded:
                self.logger.info(
                    f"Step {step_name} requires human input, waiting for interaction"
                )
                await self._wait_for_interaction_resolution(execution_id)

            # If the step requires manual approval, wait for interaction
            elif result.reason == OccurrenceReasonPendingApproval:
                self.logger.info(
                    f"Step {step_name} require manual approval, waiting for interaction"
                )
                await self._wait_for_interaction_resolution(execution_id)

            return result

        except Exception as e:
            self.logger.error(f"Step execution failed for agent {agent_id}: {e}")
            raise

    async def _update_step_run_with_result(
        self, step_run: StepRun, result: StepExecutionResponse
    ) -> None:
        """Update the step run in the database with the execution result.

        Args:
            step_run: The StepRun object to update
            result: The execution result containing outputs and status

        """
        self.logger.info(
            f"Updating step run {step_run.id} with status {result.status} and "
            f"{len(result.outputs)} outputs"
        )

        try:
            update_data = {}

            if result.status:
                update_data["status"] = result.status

            if result.outputs:
                update_data["outputs"] = result.outputs

            # Update the step run if there's data to update
            if update_data:
                step_run_update = StepRunUpdate(**update_data)
                await update_step_run(step_run.id, step_run_update)
                self.logger.info(f"Successfully updated step run {step_run.id}")
            else:
                self.logger.info(f"No updates needed for step run {step_run.id}")

        except Exception as e:
            self.logger.error(f"Failed to update step run {step_run.id}: {e}")
            raise

    async def _wait_for_interaction_resolution(self, execution_id: uuid.UUID) -> None:
        """Wait for an interaction to be resolved via notification.

        Args:
            execution_id: ID of the execution to wait for

        """
        event = await self.state_store.get_event(execution_id)
        if not event:
            self.logger.warning(f"No event found for execution {execution_id}")
            return

        # Wait for the event to be set by interaction notification
        await event.wait()

        # Reset the event for future use
        event.clear()

    async def _create_step_run(
        self,
        workflow_name: str,
        step: Step,
        previous_results: dict[str, StepExecutionResponse],
        execution_id: uuid.UUID,
        triggering_data: list[InputData] | None = None,
    ) -> StepRun:
        """Create a StepRun object with input dependencies resolved.

        Args:
            workflow_name: Name of the workflow to execute
            step: The workflow step
            previous_results: Results from previous steps
            execution_id: Current execution ID
            triggering_data: Input data for the workflow to start

        Returns:
            StepRun object

        """
        # Build input data based on step inputs
        input_blocks = []

        # Create a map of triggering data by step_block_name if it exists
        triggering_data_map = {}
        if triggering_data:
            for input_data_item in triggering_data:
                triggering_data_map[input_data_item.step_block_name] = (
                    input_data_item.data
                )

        # Process each input block defined in the step
        for step_input in step.inputs:
            # Check if this input should be fed from triggering data (first step)
            if triggering_data and step_input.step_block_name in triggering_data_map:
                # Use data from triggering data
                block_data = triggering_data_map[step_input.step_block_name]
            elif step_input.previous_step_fed:
                # Get data from previous step
                prev_step_info = step_input.previous_step_fed
                if isinstance(prev_step_info, dict) and "step_name" in prev_step_info:
                    source_step_name = prev_step_info["step_name"]
                    source_block_name = prev_step_info.get(
                        "step_block_name", step_input.step_block_name
                    )
                    if source_step_name in previous_results:
                        # Find the matching output block from the previous step
                        prev_step_outputs = previous_results[source_step_name].outputs
                        block_data = None
                        for output_block in prev_step_outputs:
                            if output_block.get("step_block_name") == source_block_name:
                                block_data = output_block.get("data")
                                break
                        if block_data is None:
                            # Default to empty data if not found
                            block_data = get_default_block_data(
                                step_input.step_block_type
                            )
                    else:
                        # Default to empty data if previous step not found
                        block_data = get_default_block_data(step_input.step_block_type)
                else:
                    # Default to empty data if previous_step_fed format is incorrect
                    block_data = get_default_block_data(step_input.step_block_type)
            else:
                # Use default empty data
                block_data = get_default_block_data(step_input.step_block_type)

            # Create step block run
            step_block_run = {
                "step_block_name": step_input.step_block_name,
                "step_block_type": step_input.step_block_type,
                "order_number": step_input.order_number,
                "status": step_input.status,
                "type_extra": step_input.type_extra,
                "default_expanded": step_input.default_expanded,
                "edited": False,
                "data": block_data,
            }

            # Add previous_step_fed if it exists
            if step_input.previous_step_fed:
                step_block_run["previous_step_fed"] = step_input.previous_step_fed

            input_blocks.append(step_block_run)

        # Create step run via gen_os_am_workflows
        step_run_request = StepRunCreate(
            step_id=step.id,
            inputs=input_blocks,
            status=StepRunStatusRunning,
        )
        step_run: StepRun = await create_step_run(
            create=step_run_request,
            workflow_execution_id=execution_id,
            workflow_name=workflow_name,
        )

        return step_run

    async def _communicate_with_agent(
        self,
        step_run: StepRun,
        agent_id: str,
        context_id: str | None = None,
    ) -> Any:
        """Communicate with an agent via A2A protocol.

        Args:
            step_run: StepRun data containing inputs and metadata
            agent_id: Agent ID for filtering and context
            context_id: Context ID for the step run

        Returns:
            Agent response

        """
        self.logger.info(
            f"Communicating with agent {agent_id} for step run {step_run.id}"
        )

        try:
            # Create A2A client
            async with httpx.AsyncClient(
                timeout=self.settings.WORKFLOW_AGENT_TIMEOUT
            ) as httpx_client:
                resolver = A2ACardResolver(
                    httpx_client=httpx_client,
                    base_url=await self._get_agent_url(agent_id),
                )
                agent_card = await resolver.get_agent_card()

                client = A2AClient(
                    httpx_client=httpx_client,
                    agent_card=agent_card,
                )

                # Create message parts from step run inputs
                message_parts = create_message_parts_from_step_run(step_run)

                # Get step name from step run
                step_name = step_run.step.name if step_run.step else "unknown_step"

                # Create message with proper metadata
                message = Message(
                    role="user",
                    contextId=context_id,
                    messageId=uuid.uuid4().hex,
                    parts=message_parts,
                )

                # Send message to agent with step metadata
                send_request = SendMessageRequest(
                    id=str(uuid.uuid4()),
                    params=MessageSendParams(
                        metadata={"step_name": step_name},
                        message=message,
                    ),
                )

                # TODO(Henrique): We need to be able to do this async
                # Review A2A SDK to check if this is already available
                response = await client.send_message(send_request)

                # Handle JSONRPC errors
                if isinstance(response.root, JSONRPCErrorResponse):
                    msg = response.root.error.model_dump_json(indent=2)
                    self.logger.error(f"JSONRPC Error: {msg}")
                    raise Exception(f"JSONRPC Error: {msg}")

                task_result = response.root.result

                self.logger.info(
                    f"Received agent response for agent {agent_id}: {task_result}"
                )
                return task_result

        except Exception as e:
            self.logger.error(f"Agent communication failed for agent {agent_id}: {e}")
            raise e

    async def _process_agent_response(
        self,
        agent_response: Task,
        workflow_name: str,
        step_run: StepRun,
        execution_id: uuid.UUID,
        agent_id: str | None = None,
    ) -> StepExecutionResponse:
        """Process agent response and determine next steps.

        Args:
            agent_response: A2A Task response from the agent
            workflow_name: Name of the workflow to execute
            step_run: Current workflow StepRun
            execution_id: Current execution ID
            agent_id: Agent ID for filtering and context

        Returns:
            Dictionary containing processed results

        """
        self.logger.info(
            f"Processing agent response for step {step_run.step.name} "
            f"(agent {agent_id}): {agent_response.status.state}"
        )

        try:
            # Check if task succeeded based on TaskState
            if agent_response.status.state not in [
                TaskState.completed,
                TaskState.working,
                TaskState.input_required,
                TaskState.submitted,
            ]:
                # Task failed - create error occurrence
                error_message = (
                    agent_response.status.message.parts[0].root.text
                    if agent_response.status.message
                    and agent_response.status.message.parts
                    else "Agent task failed"
                )
                await self._create_occurrence(
                    workflow_name=workflow_name,
                    execution_id=execution_id,
                    step_run_id=step_run.id,
                    occurrence_reason=OccurrenceReasonError,
                    message=error_message,
                )
                return StepExecutionResponse(reason=OccurrenceReasonError)

            # Handle input_required state - needs human intervention
            elif agent_response.status.state == TaskState.input_required:
                # Extract message from agent
                assistance_message = (
                    agent_response.status.message.parts[0].root.text
                    if agent_response.status.message
                    and agent_response.status.message.parts
                    else "Agent requires human input"
                )
                await self._create_occurrence(
                    workflow_name=workflow_name,
                    execution_id=execution_id,
                    step_run_id=step_run.id,
                    occurrence_reason=OccurrenceReasonAssistanceNeeded,
                    message=assistance_message,
                )
                # Extract outputs from artifacts
                outputs = extract_outputs_from_artifacts(
                    artifacts=agent_response.artifacts,
                    step=step_run.step,
                )
                return StepExecutionResponse(
                    reason=OccurrenceReasonAssistanceNeeded,
                    context_id=agent_response.contextId,
                    outputs=outputs,
                )

            # Handle completed or working states
            elif agent_response.status.state == TaskState.completed:
                # Extract outputs from artifacts
                outputs = extract_outputs_from_artifacts(
                    artifacts=agent_response.artifacts,
                    step=step_run.step,
                )

                reason = None
                # If the step requires manual approval, create an occurrence
                if step_run.step.human_approval_required:
                    reason = OccurrenceReasonPendingApproval
                    await self._create_occurrence(
                        workflow_name=workflow_name,
                        execution_id=execution_id,
                        step_run_id=step_run.id,
                        occurrence_reason=reason,
                    )

                return StepExecutionResponse(
                    status=StepRunStatusCompleted,
                    outputs=outputs,
                    context_id=agent_response.contextId,
                    reason=reason,
                )

            else:
                # Default handling for other states (working, submitted, etc.)
                return StepExecutionResponse(
                    status=StepRunStatusRunning,
                    outputs=[],
                    context_id=agent_response.contextId,
                )

        except Exception as e:
            self.logger.error(
                f"Failed to process agent response for agent {agent_id}: {e}"
            )
            raise

    async def _create_workflow_execution(
        self,
        workflow_name: str,
        test_execution: bool = False,
        agent_id: str | None = None,
    ) -> WorkflowExecution:
        """Create a new workflow execution.

        Args:
            workflow_name: Name of the workflow to execute
            input_data: Input data for the workflow
            test_execution: Whether to create a test execution
            agent_id: Agent ID for filtering and context

        Returns:
            Created WorkflowExecution object

        """
        execution = await create_workflow_execution(
            create=WorkflowExecutionCreate(
                status=WorkflowExecutionStatusRunning,
                test_execution=test_execution,
            ),
            workflow_name=workflow_name,
        )
        return execution

    async def _create_occurrence(
        self,
        workflow_name: str,
        execution_id: uuid.UUID,
        step_run_id: uuid.UUID,
        occurrence_reason: OccurrenceReasons,
        message: str,
    ) -> None:
        """Create an occurrence for a step run.

        Args:
            workflow_name: Name of the workflow
            execution_id: ID of the workflow execution
            step_run_id: ID of the step run (optional)
            occurrence_reason: Type of occurrence (error, assistance_needed, etc.)
            message: Human-readable message

        """
        self.logger.info(
            f"Creating {occurrence_reason} occurrence for step run {step_run_id} "
            f"(execution {execution_id}): {message}"
        )

        # Create occurrence via gen_os_am_workflows
        occurrence = await create_occurrence(
            create=OccurrenceCreate(reason=occurrence_reason, message=message),
            workflow_name=workflow_name,
            workflow_execution_id=execution_id,
            step_run_id=step_run_id,
        )

        self.logger.info(
            f"Created occurrence {occurrence.id} with reason {occurrence_reason} "
            f"for step run {step_run_id} (execution {execution_id})"
        )
