"""FastAPI router for issue creation and management endpoints."""

import logging
import uuid
from datetime import datetime
from typing import get_args

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, Response
from sqlalchemy.exc import IntegrityError

from gen_os_am_core.api.schemas import (
    IncidentSoftDeleteRequest,
    IncidentSoftDeleteResponse,
    IssueCreateRequest,
    IssueListFilters,
    IssueListItem,
    IssueResponse,
    IssueUpdateRequest,
    IssueUpdateResponse,
    IssueWithIncidents,
    PaginatedIssuesResponse,
)
from gen_os_am_core.api.utils.issue_utils import (
    create_issue_with_incidents,
    fetch_issue_incidents,
    fetch_issue_record,
    get_agent_issues_paginated,
    get_issue_summary,
    soft_delete_restore_incident,
    update_issue_fields,
)
from gen_os_am_core.database.session import SessionManager
from gen_os_am_core.models.enums import IssueCategory, IssueState


class AgentIssuesRouter:
    """Agent issues router for the Agent Manager API."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the agent issues router.

        Args:
            logger: Optional logger instance

        """
        self.logger = logger or logging.getLogger(__name__)

        # Router for all agent-scoped operations (with agent_id)
        self.router_agent_scoped = APIRouter(
            prefix="/agents/{agent_id}/core/v1",
            dependencies=[Depends(self._agent_id_dependency)],
            tags=["Agent Issues"],
        )

        self._setup_routes()

    @staticmethod
    def _agent_id_dependency(agent_id: str = Path(...)):
        """Extract agent_id from the path.

        This allows us to add agent_id to the router prefix without
        adding it to every endpoint signature.
        """
        return agent_id

    def _setup_routes(self):
        """Set up the agent issues routes."""

        @self.router_agent_scoped.post(
            "/issue",
            response_model=IssueResponse,
            summary="Create a new issue",
            description="""Create a new issue with optional
            related conversation and workflow incidents.""",
            status_code=201,
        )
        async def create_issue(request: Request, issue_request: IssueCreateRequest):
            """Create a new issue and related incidents for a specific agent.

            Args:
                request: FastAPI request object used to extract path params and headers.
                issue_request: Parsed request body containing issue details.

            Returns:
                IssueResponse: The created issue with its nested incidents.

            """
            agent_id = request.path_params["agent_id"]
            created_by = request.headers.get("X-User-ID")

            try:
                async with SessionManager.get_session() as db:
                    issue = await create_issue_with_incidents(
                        db,
                        agent_id=agent_id,
                        description=issue_request.description,
                        issue_type=issue_request.issue_type,
                        state=issue_request.state,
                        reported_incidents_conversations=issue_request.reported_incidents_conversations,
                        reported_incidents_workflows=issue_request.reported_incidents_workflows,
                        created_by=created_by,
                    )
                    return IssueResponse.model_validate(issue)

            except IntegrityError as e:
                self.logger.warning(f"Integrity error creating issue: {e}")
                # Rollback transaction on error
                await db.rollback()
                raise HTTPException(
                    status_code=409,
                    detail="Issue creation failed due to constraint violation.",
                ) from e
            except Exception as e:
                self.logger.error("Unexpected error in create_issue", exc_info=True)
                await db.rollback()
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router_agent_scoped.get(
            "/issue/{issue_id}",
            response_model=IssueWithIncidents,
            summary="Get a specific issue by ID",
            description="""Retrieve full details for a single issue,
            including its reported incidents.""",
        )
        async def get_issue_detail(
            request: Request,
            issue_id: uuid.UUID,
        ):
            """Retrieve one issue with its incidents."""
            agent_id = request.path_params["agent_id"]

            try:
                async with SessionManager.get_session() as db:
                    issue_row = await fetch_issue_record(db, issue_id, agent_id)
                    if issue_row is None:
                        raise HTTPException(
                            status_code=404,
                            detail=f"""Issue with ID {issue_id}
                            not found for agent {agent_id}""",
                        )

                    incidents = await fetch_issue_incidents(
                        db=db,
                        issue_id=issue_id,
                        incident_type="all",
                        incident_sort="desc",
                    )

                    response_payload = IssueWithIncidents(
                        id=issue_row.id,
                        description=issue_row.description,
                        issue_type=issue_row.issue_type,
                        state=issue_row.state,
                        close_desc=issue_row.close_desc,
                        agent=issue_row.agent_id,
                        created_at=issue_row.created_at,
                        updated_at=issue_row.updated_at,
                        reported_incidents=incidents,
                    )

                    return response_payload

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in get_issue_detail: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.patch(
            "/incident/{incident_id}",
            response_model=IncidentSoftDeleteResponse,
            summary="Soft delete or restore an incident",
            description="""Mark an incident as deleted (soft delete) or restore it
            by toggling the `is_deleted` flag.""",
        )
        async def soft_delete_incident(
            request: Request,
            incident_id: uuid.UUID,
            payload: IncidentSoftDeleteRequest,
        ):
            """Soft delete (or restore) a conversation/workflow incident."""
            agent_id = request.path_params["agent_id"]

            try:
                async with SessionManager.get_session() as db:
                    incident, kind = await soft_delete_restore_incident(
                        db=db,
                        agent_id=agent_id,
                        incident_id=incident_id,
                        payload=payload,
                    )

                    return IncidentSoftDeleteResponse(
                        id=incident.id,
                        is_deleted=incident.is_deleted,
                        deleted_at=incident.deleted_at,
                        deleted_by=incident.deleted_by,
                        kind=kind,
                    )
            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except Exception as e:
                self.logger.error(
                    "Unexpected error in soft_delete_incident", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.patch(
            "/issue/{issue_id}",
            response_model=IssueUpdateResponse,
            summary="Update an issue",
            description="""Update issue fields like description, issue_type,
            state, and close_desc.""",
        )
        async def update_issue(
            request: Request,
            issue_id: uuid.UUID,
            update_data: IssueUpdateRequest,
        ):
            """Update an existing issue for a specific agent."""
            agent_id = request.path_params["agent_id"]

            try:
                async with SessionManager.get_session() as db:
                    issue = await update_issue_fields(
                        db=db,
                        agent_id=agent_id,
                        issue_id=issue_id,
                        update_data=update_data,
                    )
                    return IssueUpdateResponse.model_validate(issue)

            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in update_issue: {e}", exc_info=True
                )
                await db.rollback()
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.get(
            "/issues",
            response_model=PaginatedIssuesResponse,
            summary="List issues for a specific agent",
            description="""List issues for a specific agent with advanced filtering and pagination.""",
        )
        async def list_agent_issues(
            request: Request,
            response: Response,
            filters: IssueListFilters = Query(),
        ):
            """List issues for a specific agent with filtering and pagination."""
            agent_id = request.path_params["agent_id"]

            try:
                async with SessionManager.get_session() as db:
                    issue_records, pagination = await get_agent_issues_paginated(
                        db=db,
                        agent_id=agent_id,
                        filters=filters,
                    )

                    issue_items = [IssueListItem(**rec) for rec in issue_records]

                    # Include summary only when showing open issues
                    summary_payload = None
                    if filters.is_open:
                        summary_payload = await get_issue_summary(db, agent_id)

                    response.headers["X-Total-Count"] = str(pagination.total_items)

                    return PaginatedIssuesResponse(
                        items=issue_items,
                        pagination=pagination,
                        summary=summary_payload,
                    )

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in list_agent_issues: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.get(
            "/issue-state",
            response_model=list[str],
            summary="Get possible issue states for agent",
            description="Retrieve a list of all possible states for an issue in the context of a specific agent.",
        )
        async def get_agent_issue_states(request: Request) -> list[str]:
            """Return all possible states for an issue for a specific agent."""
            # agent_id is available via request.path_params["agent_id"] if needed for future logic
            return list(get_args(IssueState))

        @self.router_agent_scoped.get(
            "/issue-types",
            response_model=list[str],
            summary="Get possible issue types for agent",
            description="Retrieve a list of all possible types for an issue in the context of a specific agent.",
        )
        async def get_agent_issue_types(request: Request) -> list[str]:
            """Return all possible types for an issue for a specific agent."""
            # agent_id is available via request.path_params["agent_id"] if needed for future logic
            return list(get_args(IssueCategory))

    def get_router(self) -> APIRouter:
        """Get the configured router.

        Returns:
            The agent-scoped router with all the issue management routes.

        """
        return self.router_agent_scoped
