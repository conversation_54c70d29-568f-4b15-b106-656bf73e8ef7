"""FastAPI router for issue creation and management endpoints."""

import logging
import uuid
from collections.abc import Callable
from datetime import datetime
from functools import partial
from typing import get_args

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, Response
from gen_os_sdk_emulator.core.database.crud import get_basemodel_from_table
from gen_os_sdk_emulator.core.database.session import get_session
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from gen_os_am_core.api.schemas import (
    AddIncidentsRequest,
    IncidentSoftDeleteRequest,
    IssueCreateRequest,
    IssueListFilters,
    IssueListItem,
    IssueUpdateRequest,
    PaginatedIssuesResponse,
)
from gen_os_am_core.api.utils.issue_service import (
    add_incidents_to_issue,
    fetch_issue_record,
    get_agent_issues_paginated,
    get_issue_summary,
    soft_delete_restore_incident,
    update_issue_fields,
)
from gen_os_am_core.database.models.agent_issue import (
    Issue,
    ReportedIncidentConversation,
    ReportedIncidentWorkflow,
)
from gen_os_am_core.models.enums import IssueCategory, IssueState
from gen_os_am_core.settings import DatabaseSettings

db_session: Callable = partial(get_session, DatabaseSettings.get_settings())


class AgentIssuesRouter:
    """Agent issues router for the Agent Manager API."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the agent issues router.

        Args:
            logger: Optional logger instance

        """
        self.logger = logger or logging.getLogger(__name__)

        # Router for all agent-scoped operations (with agent_id)
        self.router_agent_scoped = APIRouter(
            prefix="/agents/{agent_id}/core/v1",
            dependencies=[Depends(self._agent_id_dependency)],
            tags=["Agent Issues"],
        )

        self._setup_routes()

    @staticmethod
    def _agent_id_dependency(agent_id: str = Path(...)):
        """Extract agent_id from the path.

        This allows us to add agent_id to the router prefix without
        adding it to every endpoint signature.
        """
        return agent_id

    def _setup_routes(self):
        """Set up the agent issues routes."""

        @self.router_agent_scoped.post(
            "/issue",
            response_model=get_basemodel_from_table(Issue, relationship_depth=2),
            summary="Create a new issue",
            description="""Create a new issue with optional
            related conversation and workflow incidents.""",
            status_code=201,
        )
        async def create_issue(
            request: Request,
            issue_request: IssueCreateRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Create a new issue and related incidents for a specific agent.

            Args:
                request: FastAPI request object used to extract path params and headers.
                issue_request: Parsed request body containing issue details.
                db: Database session

            Returns:
                IssueResponse: The created issue with its nested incidents.

            """
            agent_id = request.path_params["agent_id"]
            created_by = request.headers.get("X-User-ID")

            try:
                # Create Issue instance
                issue = Issue(
                    agent_id=agent_id,
                    description=issue_request.description,
                    issue_type=issue_request.issue_type,
                    state=issue_request.state,
                )

                # Create related conversation incidents
                for conv in issue_request.reported_incidents_conversations:
                    incident_conv = ReportedIncidentConversation(
                        conversation_id=conv.conversation_id,
                        answer=conv.answer,
                        description=conv.description,
                        severity=conv.severity,
                        created_by=created_by,
                    )
                    issue.reported_incidents_conversations.append(incident_conv)

                # Create related workflow incidents
                for wf in issue_request.reported_incidents_workflows:
                    incident_wf = ReportedIncidentWorkflow(
                        workflow_execution_id=wf.workflow_execution_id,
                        step=wf.step,
                        description=wf.description,
                        severity=wf.severity,
                        created_by=created_by,
                    )
                    issue.reported_incidents_workflows.append(incident_wf)

                # Persist to database
                db.add(issue)
                await db.commit()

                await db.refresh(
                    issue,
                    [
                        "reported_incidents_conversations",
                        "reported_incidents_workflows",
                    ],
                )

                return issue

            except IntegrityError as e:
                self.logger.warning(f"Integrity error creating issue: {e}")
                raise HTTPException(
                    status_code=409,
                    detail="Issue creation failed due to constraint violation.",
                ) from e
            except Exception as e:
                self.logger.error("Unexpected error in create_issue", exc_info=True)
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router_agent_scoped.get(
            "/issue/{issue_id}",
            response_model=get_basemodel_from_table(Issue, relationship_depth=2),
            summary="Get a specific issue by ID",
            description="""Retrieve full details for a single issue,
            including its reported incidents.""",
        )
        async def get_issue_detail(
            request: Request,
            issue_id: uuid.UUID,
            db: AsyncSession = Depends(db_session),
        ):
            """Retrieve one issue with its incidents."""
            agent_id = request.path_params["agent_id"]

            try:
                issue_row = await fetch_issue_record(db, issue_id, agent_id)
                if issue_row is None:
                    raise HTTPException(
                        status_code=404,
                        detail=f"""Issue with ID {issue_id}
                        not found for agent {agent_id}""",
                    )

                return issue_row

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in get_issue_detail: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.patch(
            "/incident/{incident_id}",
            response_model=get_basemodel_from_table(ReportedIncidentConversation, relationship_depth=0) | get_basemodel_from_table(ReportedIncidentWorkflow, relationship_depth=0),
            summary="Soft delete or restore an incident",
            description="""Mark an incident as deleted (soft delete) or restore it
            by toggling the `is_deleted` flag.""",
        )
        async def soft_delete_incident(
            request: Request,
            incident_id: uuid.UUID,
            payload: IncidentSoftDeleteRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Soft delete (or restore) a conversation/workflow incident."""
            agent_id = request.path_params["agent_id"]

            try:
                incident, kind = await soft_delete_restore_incident(
                    db=db,
                    agent_id=agent_id,
                    incident_id=incident_id,
                    payload=payload,
                )

                return incident
            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except Exception as e:
                self.logger.error(
                    "Unexpected error in soft_delete_incident", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.patch(
            "/issue/{issue_id}",
            response_model=get_basemodel_from_table(Issue, relationship_depth=0),
            summary="Update an issue",
            description="""Update issue fields like description, issue_type,
            state, and close_desc.""",
        )
        async def update_issue(
            request: Request,
            issue_id: uuid.UUID,
            update_data: IssueUpdateRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Update an existing issue for a specific agent."""
            agent_id = request.path_params["agent_id"]

            try:
                issue = await update_issue_fields(
                    db=db,
                    agent_id=agent_id,
                    issue_id=issue_id,
                    update_data=update_data,
                )
                return issue

            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in update_issue: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.get(
            "/issues",
            response_model=PaginatedIssuesResponse,
            summary="List issues for a specific agent",
            description="""List issues for a specific
            agent with advanced filtering and pagination.""",
        )
        async def list_agent_issues(
            request: Request,
            response: Response,
            filters: IssueListFilters = Query(),
            db: AsyncSession = Depends(db_session),
        ):
            """List issues for a specific agent with filtering and pagination."""
            agent_id = request.path_params["agent_id"]

            try:
                issue_records, pagination = await get_agent_issues_paginated(
                    db=db,
                    agent_id=agent_id,
                    filters=filters,
                )

                issue_items = [IssueListItem(**rec) for rec in issue_records]

                # Include summary only when showing open issues
                summary_payload = None
                if filters.is_open:
                    summary_payload = await get_issue_summary(db, agent_id)

                response.headers["X-Total-Count"] = str(pagination.total_items)

                return PaginatedIssuesResponse(
                    items=issue_items,
                    pagination=pagination,
                    summary=summary_payload,
                )

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in list_agent_issues: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.get(
            "/issue-state",
            response_model=list[str],
            summary="Get possible issue states for agent",
            description="""Retrieve a list of all possible
            states for an issue in the context of a specific agent.""",
        )
        async def get_agent_issue_states(request: Request) -> list[str]:
            """Return all possible states for an issue for a specific agent."""
            # agent_id is available via request.path_params["agent_id"]
            # if needed for future logic
            return list(get_args(IssueState))

        @self.router_agent_scoped.get(
            "/issue-types",
            response_model=list[str],
            summary="Get possible issue types for agent",
            description="""Retrieve a list of all possible types for
            an issue in the context of a specific agent.""",
        )
        async def get_agent_issue_types(request: Request) -> list[str]:
            """Return all possible types for an issue for a specific agent."""
            # agent_id is available via request.path_params["agent_id"]
            # if needed for future logic
            return list(get_args(IssueCategory))

        @self.router_agent_scoped.post(
            "/issue/{issue_id}/incidents",
            response_model=get_basemodel_from_table(Issue, relationship_depth=2),
            summary="Add incidents to an existing issue",
            description="""Add new cnv adn wf incidents to an existing issue.
            This endpoint allows adding incidents into an existing issue without
            modifying the issue's core properties.""",
            status_code=201,
        )
        async def add_incidents_to_existing_issue(
            request: Request,
            issue_id: uuid.UUID,
            incidents_request: AddIncidentsRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Add new incidents to an existing issue for a specific agent.

            Args:
                request: FastAPI request object used to extract path params and headers.
                issue_id: UUID of the existing issue to add incidents to.
                incidents_request: Parsed request body containing incidents to add.
                db: Database session dependency.

            Returns:
                IssueResponse: The updated issue with all incidents (existing + new).

            """
            agent_id = request.path_params["agent_id"]
            created_by = request.headers.get("X-User-ID")

            try:
                issue = await add_incidents_to_issue(
                    db,
                    agent_id=agent_id,
                    issue_id=issue_id,
                    reported_incidents_conversations=incidents_request.reported_incidents_conversations,
                    reported_incidents_workflows=incidents_request.reported_incidents_workflows,
                    created_by=created_by,
                )
                return issue

            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except IntegrityError as e:
                self.logger.warning(f"Integrity error adding incidents: {e}")
                raise HTTPException(
                    status_code=409,
                    detail="Adding incidents failed due to constraint violation.",
                ) from e
            except Exception as e:
                self.logger.error(
                    "Unexpected error in add_incidents_to_existing_issue", exc_info=True
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

    def get_router(self) -> APIRouter:
        """Get the configured router.

        Returns:
            The agent-scoped router with all the issue management routes.

        """
        return self.router_agent_scoped
