"""Database models for the Case Management system.

This module defines the SQLAlchemy ORM models that represent the database schema
for the case management system. The models include workflows, steps, runs,
occurrences, interactions, and files.
"""

import uuid
from datetime import datetime
from typing import Literal

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, UniqueConstraint
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    mapped_column,
    relationship,
    validates,
)

from gen_os_am_workflows.database.exceptions import StepInputValidationError


class Base(DeclarativeBase):
    """Base class for all SQLAlchemy models."""

    pass


# Workflow Execution Statuses
WorkflowExecutionStatus = Literal[
    "running",
    "blocked_pending_approval",
    "blocked_assistance_needed",
    "error",
    "completed",
    "cancelled",
]
WorkflowExecutionStatusRunning: WorkflowExecutionStatus = "running"
WorkflowExecutionStatusCompleted: WorkflowExecutionStatus = "completed"
WorkflowExecutionStatusCancelled: WorkflowExecutionStatus = "cancelled"
WorkflowExecutionStatusBlockedPendingApproval: WorkflowExecutionStatus = "blocked_pending_approval"
WorkflowExecutionStatusBlockedAssistanceNeeded: WorkflowExecutionStatus = (
    "blocked_assistance_needed"
)
WorkflowExecutionStatusError: WorkflowExecutionStatus = "error"
WorkflowExecutionStatusBlocked: list[WorkflowExecutionStatus] = [
    WorkflowExecutionStatusBlockedPendingApproval,
    WorkflowExecutionStatusBlockedAssistanceNeeded,
]
WorkflowTerminalStatuses: list[WorkflowExecutionStatus] = [
    WorkflowExecutionStatusCompleted,
    WorkflowExecutionStatusCancelled,
]

# Step Run Statuses
StepRunStatus = Literal[
    "running",
    "blocked_pending_approval",
    "blocked_assistance_needed",
    "error",
    "completed",
    "cancelled",
]
StepRunStatusRunning: StepRunStatus = "running"
StepRunStatusBlockedPendingApproval: StepRunStatus = "blocked_pending_approval"
StepRunStatusBlockedAssistanceNeeded: StepRunStatus = "blocked_assistance_needed"
StepRunStatusError: StepRunStatus = "error"
StepRunStatusCompleted: StepRunStatus = "completed"
StepRunStatusCancelled: StepRunStatus = "cancelled"
StepRunStatusBlocked: list[StepRunStatus] = [
    StepRunStatusBlockedPendingApproval,
    StepRunStatusBlockedAssistanceNeeded,
]
StepRunTerminalStatuses: list[StepRunStatus] = [
    StepRunStatusCompleted,
    StepRunStatusCancelled,
]

# Occurrence Statuses
OccurrenceStatus = Literal["open", "solved", "unsolved"]
OccurrenceStatusOpen: OccurrenceStatus = "open"
OccurrenceStatusSolved: OccurrenceStatus = "solved"
OccurrenceStatusUnsolved: OccurrenceStatus = "unsolved"

# Occurrence Reasons
OccurrenceReasons = Literal[
    "pending_approval", "assistance_needed", "error", "edit_previous_step", "solve_manually"
]
OccurrenceReasonPendingApproval: OccurrenceReasons = "pending_approval"
OccurrenceReasonAssistanceNeeded: OccurrenceReasons = "assistance_needed"
OccurrenceReasonError: OccurrenceReasons = "error"
OccurrenceReasonEditPreviousStep: OccurrenceReasons = "edit_previous_step"
OccurrenceReasonSolveManually: OccurrenceReasons = "solve_manually"

# Interaction Kinds
InteractionKind = Literal["approved", "resumed", "edited", "cancelled", "solve_manually"]
InteractionKindApproved: InteractionKind = "approved"
InteractionKindResumed: InteractionKind = "resumed"
InteractionKindEdited: InteractionKind = "edited"
InteractionKindCancelled: InteractionKind = "cancelled"
InteractionKindSolveManually: InteractionKind = "solve_manually"

# Step Types
StepType = Literal["gen_ai", "tool", "action"]
StepTypeGenAi: StepType = "gen_ai"
StepTypeTool: StepType = "tool"
StepTypeAction: StepType = "action"

# Step Block Types
StepBlockType = Literal[
    "dataset",
    "file",
    "selection",
    "text_structured",
    "text_unstructured",
    "tool",
    "email",
]
StepBlockTypeSelection: StepBlockType = "selection"
StepBlockTypeTextStructured: StepBlockType = "text_structured"

# Step Block Statuses
StepBlockStatus = Literal["editable", "non_editable"]

# Workflow Execution History Events
WorkflowExecutionHistoryEvent = Literal[
    "execution_started",
    "completed",
    "error",
    "blocked_assistance_needed",
    "blocked_pending_approval",
    "approved",
    "resumed",
    "edited",
    "cancelled",
]
WorkflowExecutionHistoryEventExecutionStarted: WorkflowExecutionHistoryEvent = "execution_started"
WorkflowExecutionHistoryEventCompleted: WorkflowExecutionHistoryEvent = "completed"
WorkflowExecutionHistoryEventError: WorkflowExecutionHistoryEvent = "error"
WorkflowExecutionHistoryEventBlockedAssistanceNeeded: WorkflowExecutionHistoryEvent = (
    "blocked_assistance_needed"
)
WorkflowExecutionHistoryEventBlockedPendingApproval: WorkflowExecutionHistoryEvent = (
    "blocked_pending_approval"
)
WorkflowExecutionHistoryEventApproved: WorkflowExecutionHistoryEvent = "approved"
WorkflowExecutionHistoryEventResumed: WorkflowExecutionHistoryEvent = "resumed"
WorkflowExecutionHistoryEventEdited: WorkflowExecutionHistoryEvent = "edited"
WorkflowExecutionHistoryEventCancelled: WorkflowExecutionHistoryEvent = "cancelled"


class Workflow(Base):
    """A workflow definition in the case management system.

    A workflow represents a sequence of steps that need to be executed in order
    to complete a business process.
    """

    __tablename__ = "workflow"

    # TODO(Henrique): if the workflow is deleted, delete the step runs and history in cascade.

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String, nullable=False, unique=True)
    agent_id: Mapped[str] = mapped_column(String, nullable=False, unique=True)
    description: Mapped[str | None] = mapped_column(String, nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.now, onupdate=datetime.now)
    extra_fields: Mapped[list[dict]] = mapped_column(JSON, nullable=False, default=[])

    steps: Mapped[list["Step"]] = relationship(
        "Step",
        back_populates="workflow",
        cascade="all, delete-orphan",
    )
    workflow_executions: Mapped[list["WorkflowExecution"]] = relationship(
        "WorkflowExecution", back_populates="workflow"
    )


class StepInput(Base):
    """A step input in the system.

    Represents a input to a step, tracking its name, description, and type.
    """

    __tablename__ = "step_input"
    __table_args__ = (
        UniqueConstraint("step_id", "step_block_name", name="unique_step_input_block_name"),
    )

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    step_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("step.id"))
    step_block_name: Mapped[str] = mapped_column(String, nullable=False)
    step_block_type: Mapped[StepBlockType] = mapped_column(String, nullable=False)
    type_extra: Mapped[list[dict] | dict] = mapped_column(JSON, nullable=False, default={})
    order_number: Mapped[int] = mapped_column(nullable=False)
    status: Mapped[StepBlockStatus] = mapped_column(String, nullable=False)
    previous_step_fed: Mapped[dict] = mapped_column(JSON, nullable=False, default={})
    default_expanded: Mapped[bool] = mapped_column(Boolean, nullable=True, default=None)

    step: Mapped["Step"] = relationship("Step", back_populates="inputs")

    @validates("type_extra")
    def validate_type_extra(self, key, type_extra):
        """Validate that type_extra is not empty for selection and text_structured types."""
        if self.step_block_type in ["selection", "text_structured"] and not type_extra:
            raise ValueError(
                f"type_extra cannot be empty for step_block_type {self.step_block_type}"
            )
        return type_extra


class StepOutput(Base):
    """A step output in the system.

    Represents a output from a step, tracking its name, description, and type.
    """

    __tablename__ = "step_output"
    __table_args__ = (
        UniqueConstraint("step_id", "step_block_name", name="unique_step_output_block_name"),
    )

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    step_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("step.id"))
    step_block_name: Mapped[str] = mapped_column(String, nullable=False)
    step_block_type: Mapped[StepBlockType] = mapped_column(String, nullable=False)
    type_extra: Mapped[list[dict] | dict] = mapped_column(JSON, nullable=False, default={})
    order_number: Mapped[int] = mapped_column(nullable=False)
    status: Mapped[StepBlockStatus] = mapped_column(String, nullable=False)
    default_expanded: Mapped[bool] = mapped_column(Boolean, nullable=True, default=None)

    step: Mapped["Step"] = relationship("Step", back_populates="outputs")

    @validates("type_extra")
    def validate_type_extra(self, key, type_extra):
        """Validate that type_extra is not empty for selection and text_structured types."""
        if self.step_block_type in ["selection", "text_structured"] and not type_extra:
            raise ValueError(
                f"type_extra cannot be empty for step_block_type {self.step_block_type}"
            )
        return type_extra


class Step(Base):
    """A step within a workflow.

    A step represents a single task or operation that needs to be performed
    as part of a workflow.
    """

    __tablename__ = "step"
    __table_args__ = (
        UniqueConstraint("name", "workflow_id", name="unique_step_name_per_workflow"),
    )

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str | None] = mapped_column(String, nullable=True)
    step_type: Mapped[StepType] = mapped_column(String, nullable=False)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    workflow_id: Mapped[uuid.UUID] = mapped_column(
        ForeignKey("workflow.id", ondelete="CASCADE"), nullable=False
    )
    order_number: Mapped[int] = mapped_column(nullable=False)

    workflow: Mapped["Workflow"] = relationship("Workflow", back_populates="steps")
    step_runs: Mapped[list["StepRun"]] = relationship("StepRun", back_populates="step")
    solve_manually: Mapped[bool | None] = mapped_column(Boolean, nullable=True, default=None)
    human_approval_required: Mapped[bool | None] = mapped_column(
        Boolean, nullable=True, default=None
    )

    inputs: Mapped[list["StepInput"]] = relationship(
        "StepInput",
        back_populates="step",
        cascade="all, delete-orphan",
    )
    outputs: Mapped[list["StepOutput"]] = relationship(
        "StepOutput",
        back_populates="step",
        cascade="all, delete-orphan",
    )

    STEP_TYPE_INPUT_RULES = {
        "gen_ai": ["non_editable"],
        "tool": ["non_editable"],
        "action": ["editable", "non_editable"],
    }

    @validates("inputs")
    def validate_inputs(self, key, input_item):
        """Validate step input editability based on step type.

        Args:
            key: The key being validated (always 'inputs' in this case)
            input_item: The StepInput instance being validated

        Returns:
            The validated input_item if validation passes

        Raises:
            StepInputValidationError: If the input status violates the step type constraints

        """
        allowed_statuses = self.STEP_TYPE_INPUT_RULES.get(self.step_type, [])
        if input_item.status not in allowed_statuses:
            raise StepInputValidationError(
                f"Step type '{self.step_type}' only allows inputs with status(es): "
                f"{', '.join(allowed_statuses)}. Got: {input_item.status}"
            )
        return input_item

    @validates("outputs")
    def validate_outputs(self, key, output_item):
        """Validate step output based on step type.

        Args:
            key: The key being validated (always 'outputs' in this case)
            output_item: The StepOutput instance being validated

        Returns:
            The validated output_item if validation passes

        Raises:
            StepInputValidationError: If the output is not allowed for this step type
                                    or if the output status violates the step type constraints

        """
        # For tool steps, no outputs are allowed
        if self.step_type == StepTypeAction and output_item:
            raise StepInputValidationError("Step type 'action' cannot have any outputs")
        return output_item


class WorkflowExecution(Base):
    """An instance of a workflow execution.

    Represents a single execution of a workflow, tracking its progress and status.
    """

    __tablename__ = "workflow_execution"
    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    workflow_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("workflow.id"))
    started_at: Mapped[datetime] = mapped_column(default=datetime.now)
    status: Mapped[WorkflowExecutionStatus] = mapped_column(
        String, nullable=False, default="Blocked"
    )
    workflow: Mapped["Workflow"] = relationship("Workflow", back_populates="workflow_executions")
    step_runs: Mapped[list["StepRun"]] = relationship(
        "StepRun", back_populates="workflow_execution"
    )
    history: Mapped[list["WorkflowExecutionHistory"]] = relationship(
        "WorkflowExecutionHistory",
        back_populates="workflow_execution",
        cascade="all, delete-orphan",
    )
    updated_at: Mapped[datetime] = mapped_column(default=datetime.now, onupdate=datetime.now)
    finished_at: Mapped[datetime | None] = mapped_column(default=None, nullable=True)
    extra_fields: Mapped[list[dict]] = mapped_column(JSON, nullable=False, default=[])
    test_execution: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    def __init__(self, **kwargs):
        """Initialize a WorkflowExecution instance.

        Any extra fields not defined in the model will be stored in the extra_fields column.
        """
        model_fields = self.__annotations__.keys()
        extra_fields = {k: v for k, v in kwargs.items() if k not in model_fields}
        if extra_fields:
            kwargs["extra_fields"] = extra_fields
            for field in extra_fields.keys():
                kwargs.pop(field)
        super().__init__(**kwargs)

    def are_all_steps_completed(self) -> bool:
        """Check if all steps in the workflow have been completed.

        Returns:
            bool: True if all steps have corresponding completed step runs, False otherwise.

        """
        # Create a mapping of completed step runs by step_id
        completed_steps = {sr.step_id: sr.status == StepRunStatusCompleted for sr in self.step_runs}

        # Check if all workflow steps have corresponding completed step runs
        return all(completed_steps.get(step.id, False) for step in self.workflow.steps)

    def cancel_uncompleted_step_runs(self) -> "WorkflowExecution":
        """Cancel remaining step runs in the workflow execution."""
        for step_run in self.step_runs:
            if step_run.status != StepRunStatusCompleted:
                step_run.status = StepRunStatusCancelled
        return self


class WorkflowExecutionHistory(Base):
    """A history of a workflow execution.

    Represents a history of a workflow execution, tracking its progress and status.
    """

    __tablename__ = "workflow_execution_history"
    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    workflow_execution_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("workflow_execution.id"))
    event: Mapped[WorkflowExecutionHistoryEvent] = mapped_column(String, nullable=False)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    termination_message: Mapped[str | None] = mapped_column(String, nullable=True)
    error_message: Mapped[str | None] = mapped_column(String, nullable=True)
    step_name: Mapped[str | None] = mapped_column(String, nullable=True)

    workflow_execution: Mapped["WorkflowExecution"] = relationship(
        "WorkflowExecution", back_populates="history"
    )


class StepRun(Base):
    """An instance of a step execution within a workflow execution.

    Represents a single execution of a step, tracking its progress, status, and output.
    """

    __tablename__ = "step_run"
    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    workflow_execution_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("workflow_execution.id"))
    step_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("step.id"))
    started_at: Mapped[datetime] = mapped_column(default=datetime.now)
    completed_at: Mapped[datetime | None] = mapped_column(default=None, nullable=True)
    inputs: Mapped[list[dict]] = mapped_column(JSON, nullable=False, default=[])
    outputs: Mapped[list[dict]] = mapped_column(JSON, nullable=False, default=[])
    status: Mapped[StepRunStatus] = mapped_column(String, nullable=False, default="Blocked")
    edited: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)

    workflow_execution: Mapped["WorkflowExecution"] = relationship(
        "WorkflowExecution", back_populates="step_runs"
    )
    step: Mapped["Step"] = relationship("Step", back_populates="step_runs")
    occurrences: Mapped[list["Occurrence"]] = relationship("Occurrence", back_populates="step_run")

    def is_first_step(self) -> bool:
        """Check if the step run is the first step in the workflow.

        Returns:
            bool: True if the step run is the first step in the workflow, False otherwise.

        """
        return self.step.order_number == 1

    def is_solve_manually(self) -> bool:
        """Check if the step run is a solve manually step.

        Returns:
            bool: True if the step run is a solve manually step, False otherwise.

        """
        return self.step.solve_manually


class Occurrence(Base):
    """An occurrence of an issue or event during a step run.

    Represents a problem, issue, or notable event that occurred during the execution
    of a step and requires attention or intervention.
    """

    __tablename__ = "occurrence"
    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    step_run_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("step_run.id"))
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    reason: Mapped[OccurrenceReasons] = mapped_column(
        String, nullable=False, default="pending_approval"
    )
    message: Mapped[str | None] = mapped_column(String, nullable=True)
    status: Mapped[OccurrenceStatus] = mapped_column(String, nullable=False, default="Blocked")
    interactions: Mapped[list["Interaction"]] = relationship(
        "Interaction", back_populates="occurrence"
    )
    step_run: Mapped["StepRun"] = relationship("StepRun", back_populates="occurrences")


class Interaction(Base):
    """An interaction with an occurrence.

    Represents a user's interaction with an occurrence, such as solving, blocking,
    or closing the issue.
    """

    __tablename__ = "interaction"
    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    occurrence_id: Mapped[uuid.UUID] = mapped_column(ForeignKey("occurrence.id"))
    interacted_at: Mapped[datetime] = mapped_column(default=datetime.now)
    edited_data: Mapped[list[dict]] = mapped_column(JSON, nullable=False, default=[])
    kind: Mapped[InteractionKind] = mapped_column(String, nullable=False, default="approved")
    resolution: Mapped[str | None] = mapped_column(String, nullable=True, default=None)
    occurrence: Mapped["Occurrence"] = relationship("Occurrence", back_populates="interactions")


# TODO(Henrique): Associate the file with a step run.
class File(Base):
    """A file stored in the system.

    Represents a file that has been uploaded to the system, tracking its storage
    location and metadata.
    """

    __tablename__ = "file"

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    storage_path: Mapped[str] = mapped_column(String, nullable=False)
    file_name: Mapped[str] = mapped_column(String, nullable=False)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.now, onupdate=datetime.now)
