"""Pydantic schemas for agent management API."""

from pydantic import BaseModel, Field


class AgentCreateRequest(BaseModel):
    """Schema for creating an agent."""

    id: str = Field(..., description="ID of the agent")
    name: str = Field(..., description="Name of the agent")
    url: str = Field(..., description="URL of the agent")
    description: str | None = Field(None, description="Description of the agent")


class AgentUpdateRequest(BaseModel):
    """Schema for updating an agent."""

    name: str | None = Field(None, description="Name of the agent")
    url: str | None = Field(None, description="URL of the agent")
    description: str | None = Field(None, description="Description of the agent")
