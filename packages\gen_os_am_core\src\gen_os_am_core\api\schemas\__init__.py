"""Pydantic schemas for agent configuration management API."""

# Common schemas
# Agent configuration schemas
from .agent_config import (
    AgentConfigurationBase,
    AgentConfigurationItem,
    AgentConfigurationResponse,
    AgentConfigurationSyncRequest,
)

# Agent issues schemas
from .agent_issues import (
    AddIncidentsRequest,
    IncidentSoftDeleteRequest,
    IncidentSoftDeleteResponse,
    IssueCreateRequest,
    IssueListFilters,
    IssueListItem,
    IssueResponse,
    IssueUpdateRequest,
    IssueUpdateResponse,
    IssueWithIncidents,
    PaginatedIssuesResponse,
    ReportedIncidentConversationBase,
    ReportedIncidentConversationCreate,
    ReportedIncidentConversationResponse,
    ReportedIncidentConversationUnified,
    ReportedIncidentWorkflowBase,
    ReportedIncidentWorkflowCreate,
    ReportedIncidentWorkflowResponse,
    ReportedIncidentWorkflowUnified,
)

# Agent schemas
from .agents import AgentCreateRequest, AgentUpdateRequest
from .common import PaginationDetails, PaginationFilters

__all__ = [
    # Common schemas
    "PaginationDetails",
    "PaginationFilters",
    # Agent configuration schemas
    "AgentConfigurationBase",
    "AgentConfigurationItem",
    "AgentConfigurationResponse",
    "AgentConfigurationSyncRequest",
    # Agent schemas
    "AgentCreateRequest",
    "AgentUpdateRequest",
    # Agent issues schemas
    "AddIncidentsRequest",
    "IncidentSoftDeleteRequest",
    "IncidentSoftDeleteResponse",
    "IssueCreateRequest",
    "IssueListFilters",
    "IssueListItem",
    "IssueResponse",
    "IssueUpdateRequest",
    "IssueUpdateResponse",
    "IssueWithIncidents",
    "PaginatedIssuesResponse",
    "ReportedIncidentConversationBase",
    "ReportedIncidentConversationCreate",
    "ReportedIncidentConversationResponse",
    "ReportedIncidentConversationUnified",
    "ReportedIncidentWorkflowBase",
    "ReportedIncidentWorkflowCreate",
    "ReportedIncidentWorkflowResponse",
    "ReportedIncidentWorkflowUnified",
]
