"""Case Management API Router."""

import logging
import uuid
from collections.abc import Callable
from functools import partial
from typing import Literal

import yaml
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends, HTTPException, Path, Request, Response, UploadFile
from gen_os_sdk_emulator.core.database.crud import get_basemodel_from_table
from gen_os_sdk_emulator.core.database.session import get_session
from gen_os_sdk_emulator.infrastructure.storage import FileMetadata
from sqlalchemy import String, delete, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, noload, selectinload

from gen_os_am_workflows.api.containers import Container, StorageInterface
from gen_os_am_workflows.api.models.request import (
    FileUploadRequest,
    InteractionCreate,
    InteractionUpdate,
    OccurrenceCreate,
    OccurrenceUpdate,
    StepCreate,
    StepRunCreate,
    StepRunUpdate,
    StepUpdate,
    WorkflowCreate,
    WorkflowExecutionCreate,
    WorkflowExecutionUpdate,
    WorkflowUpdate,
)
from gen_os_am_workflows.api.models.response import (
    FileDownloadResponse,
    FileUploadResponse,
    MessageResponse,
)
from gen_os_am_workflows.api.utils.state_change_cascade import (
    InteractionStateChangeCascade,
    OccurrenceStateChangeCascade,
    StepRunStateChangeCascade,
)
from gen_os_am_workflows.api.utils.step_run_block_updater import StepRunBlockUpdater
from gen_os_am_workflows.api.utils.step_run_validator import StepRunValidator
from gen_os_am_workflows.api.webhooks import WebhooksService
from gen_os_am_workflows.database.exceptions import StepInputValidationError
from gen_os_am_workflows.database.models import (
    File,
    Interaction,
    InteractionKindSolveManually,
    Occurrence,
    OccurrenceReasonSolveManually,
    OccurrenceStatusOpen,
    Step,
    StepInput,
    StepOutput,
    StepRun,
    StepRunStatusBlocked,
    StepRunStatusCompleted,
    StepRunStatusError,
    StepRunStatusRunning,
    StepTypeAction,
    Workflow,
    WorkflowExecution,
    WorkflowExecutionHistory,
    WorkflowExecutionStatus,
    WorkflowExecutionStatusBlocked,
    WorkflowTerminalStatuses,
)
from gen_os_am_workflows.settings import DatabaseSettings

db_session: Callable = partial(get_session, DatabaseSettings.get_settings())

# Configure logging
logger = logging.getLogger(__name__)


def agent_id_dependency(agent_id: str = Path(...)):
    """Extract agent_id from the path.

    This allows us to add agent_id to the router prefix without
    adding it to every endpoint signature.
    """
    return agent_id


router = APIRouter(dependencies=[Depends(agent_id_dependency)])

# TODO(Henrique): Return the database object instead of a message in PATCH endpoints.


@router.post(
    "/workflows",
    response_model=get_basemodel_from_table(Workflow, relationship_depth=0),
)
async def create_workflow(create: WorkflowCreate, db: AsyncSession = Depends(db_session)):
    """Create a new workflow."""
    db_workflow = Workflow(**create.model_dump(exclude_none=True))
    db.add(db_workflow)
    await db.commit()
    await db.refresh(db_workflow)
    return db_workflow


@router.post(
    "/workflows/batch", response_model=get_basemodel_from_table(Workflow, relationship_depth=0)
)
async def create_workflow_with_steps(
    request: Request, workflow_file: UploadFile, db: AsyncSession = Depends(db_session)
):
    """Create a workflow with all its steps in a single operation from a YAML file.

    Args:
        request: The request object.
        workflow_file: A YAML file containing the workflow and its steps.
        agent_id: The ID of the agent that the workflow belongs to.
        db: The database session.

    Expected format:
        workflow:
          name: str
          description: str
          extra_fields:
            - field_name: str
              field_type: str
          steps:
            - name: str
              description: str
              ...

    """
    agent_id = request.path_params["agent_id"]

    # Read and parse the YAML file
    content = await workflow_file.read()
    workflow_dict: dict = yaml.safe_load(content)

    workflow_data: dict = workflow_dict["workflow"]
    steps_data: list[dict] = workflow_data.pop("steps")

    # Create workflow first
    db_workflow = Workflow(**workflow_data, agent_id=agent_id)
    db.add(db_workflow)

    # Create and add each step
    for step_data in steps_data:
        db_step = Step(**{k: v for k, v in step_data.items() if k not in ["inputs", "outputs"]})

        # Add inputs
        for input_data in step_data.get("inputs", []):
            db_step.inputs.append(StepInput(**input_data))

        # Add outputs
        for output_data in step_data.get("outputs", []):
            db_step.outputs.append(StepOutput(**output_data))

        db_workflow.steps.append(db_step)

    await db.commit()
    await db.refresh(db_workflow)
    return db_workflow


@router.post(
    "/workflows/{workflow_name}/steps",
    response_model=get_basemodel_from_table(Step, relationship_depth=0),
)
async def create_step(
    create: StepCreate, workflow_name: str, db: AsyncSession = Depends(db_session)
):
    """Create a new step for a workflow."""
    stmt = (
        select(Workflow).where(Workflow.name == workflow_name).options(joinedload(Workflow.steps))
    )
    workflow = await db.scalar(stmt)
    if not workflow:
        raise HTTPException(status_code=400, detail=f"Workflow {workflow_name} does not exist.")

    db_entity = Step(**create.model_dump(exclude_none=True, exclude={"inputs", "outputs"}))

    try:
        for input_block in create.inputs:
            db_entity.inputs.append(StepInput(**input_block.model_dump(exclude_none=True)))

        for output_block in create.outputs:
            db_entity.outputs.append(StepOutput(**output_block.model_dump(exclude_none=True)))
    except StepInputValidationError as e:
        raise HTTPException(status_code=422, detail=str(e)) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

    workflow.steps.append(db_entity)
    db.add(workflow)
    await db.commit()
    await db.refresh(db_entity)
    await db.flush()
    return db_entity


@router.post(
    "/workflows/{workflow_name}/workflow-executions",
    response_model=get_basemodel_from_table(WorkflowExecution, relationship_depth=0),
)
async def create_workflow_execution(
    create: WorkflowExecutionCreate, workflow_name: str, db: AsyncSession = Depends(db_session)
):
    """Create a new workflow execution."""
    stmt = (
        select(Workflow)
        .where(Workflow.name == workflow_name)
        .options(joinedload(Workflow.workflow_executions))
    )
    workflow = await db.scalar(stmt)
    if not workflow:
        raise HTTPException(status_code=400, detail=f"Workflow {workflow_name} does not exist.")

    db_entity = WorkflowExecution(**create.model_dump(exclude_none=True))
    workflow.workflow_executions.append(db_entity)
    db.add(workflow)
    await db.commit()
    await db.refresh(db_entity)
    await db.flush()
    return db_entity


@router.post(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs",
    response_model=get_basemodel_from_table(StepRun, relationship_depth=0),
)
async def create_step_run(
    create: StepRunCreate,
    workflow_execution_id: uuid.UUID,
    workflow_name: str,
    db: AsyncSession = Depends(db_session),
):
    """Create a new step run for a workflow execution."""
    execution_stmt = (
        select(WorkflowExecution)
        .where(WorkflowExecution.id == workflow_execution_id)
        .options(
            joinedload(WorkflowExecution.step_runs),
            joinedload(WorkflowExecution.workflow),
        )
    )
    execution = await db.scalar(execution_stmt)

    if not execution:
        raise HTTPException(
            status_code=400, detail=f"Execution {workflow_execution_id} does not exist."
        )

    step_stmt = (
        select(Step)
        .where(Step.id == create.step_id)
        .options(joinedload(Step.inputs), joinedload(Step.outputs))
    )
    step = await db.scalar(step_stmt)

    if execution.status in WorkflowTerminalStatuses:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot create step run - workflow execution {workflow_execution_id} "
            f"is in terminal status {execution.status}",
        )

    if not step:
        raise HTTPException(status_code=400, detail=f"Step {create.step_id} does not exist.")

    if (
        create.status == StepRunStatusCompleted
        and not create.outputs
        and step.step_type != StepTypeAction  # Action steps don't have outputs
    ):
        raise HTTPException(
            status_code=422,
            detail="Output data is required for Step Run status Complete",
        )

    if create.status in StepRunStatusBlocked:
        raise HTTPException(
            status_code=400,
            detail="Blocked status must be set via occurrence",
        )

    # Validate StepRun against Step structure definition
    StepRunValidator(step, create).validate()

    db_entity = StepRun(
        **create.model_dump(exclude_none=True, exclude={"step_id", "occurrence"}),
        step_id=step.id,
    )

    execution.step_runs.append(db_entity)
    db.add(execution)

    await db.commit()
    await db.refresh(db_entity)
    # Eagerly load all relationships needed for the cascade
    await db.refresh(db_entity.workflow_execution, ["workflow"])
    await db.refresh(db_entity.workflow_execution.workflow, ["steps"])
    await db.flush()

    await StepRunStateChangeCascade(db_entity, db).run()

    return db_entity


@router.post(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs/{step_run_id}/occurrences",
    response_model=get_basemodel_from_table(Occurrence, relationship_depth=0),
)
async def create_occurrence(
    create: OccurrenceCreate,
    workflow_name: str,
    workflow_execution_id: uuid.UUID,
    step_run_id: uuid.UUID,
    db: AsyncSession = Depends(db_session),
):
    """Create a new occurrence for a step run."""
    step_run = await db.scalar(
        select(StepRun)
        .options(joinedload(StepRun.workflow_execution), joinedload(StepRun.occurrences))
        .where(StepRun.id == step_run_id)
    )
    if not step_run:
        raise HTTPException(status_code=400, detail=f"Step Run {step_run_id} does not exist")

    if step_run.status in StepRunStatusBlocked and step_run.occurrences:
        raise HTTPException(
            status_code=400,
            detail=f"Step Run {step_run_id} is already blocked by an Occurrence",
        )

    db_entity = Occurrence(
        **create.model_dump(exclude_none=True),
        status=OccurrenceStatusOpen,
        step_run_id=step_run_id,
    )
    db.add(db_entity)
    await db.commit()
    await db.refresh(db_entity)

    # Eagerly load all relationships needed for the cascade
    await db.refresh(db_entity, ["step_run"])
    await db.refresh(db_entity.step_run, ["step"])
    await db.flush()

    await OccurrenceStateChangeCascade(db_entity, db).run()

    return db_entity


@router.post(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-run/{step_run_id}/occurrences/{occurrence_id}/interactions",
    response_model=get_basemodel_from_table(Interaction, relationship_depth=0),
)
@inject
async def create_interaction(
    request: Request,
    create: InteractionCreate,
    occurrence_id: uuid.UUID,
    workflow_name: str,
    workflow_execution_id: uuid.UUID,
    step_run_id: uuid.UUID,
    webhooks_service: WebhooksService = Depends(Provide[Container.webhooks_service]),
    db: AsyncSession = Depends(db_session),
):
    """Create a new interaction for an occurrence."""
    occurrence = await db.scalar(
        select(Occurrence)
        .options(
            joinedload(Occurrence.step_run)
            .joinedload(StepRun.workflow_execution)
            .options(
                joinedload(WorkflowExecution.step_runs),
                joinedload(WorkflowExecution.workflow).joinedload(Workflow.steps),
            )
        )
        .where(Occurrence.id == occurrence_id)
    )

    if not occurrence:
        raise HTTPException(status_code=400, detail=f"Occurrence {occurrence_id} does not exist")

    if occurrence.status != OccurrenceStatusOpen:
        raise HTTPException(
            status_code=400,
            detail=f"Occurrence {occurrence_id} is in state {occurrence.status}, not Open.",
        )

    # Update the step run with the edited data
    if create.edited_data:
        try:
            block_updater = StepRunBlockUpdater(occurrence.step_run)
            for block in create.edited_data:
                block_updater.update_output(block.step_block_name, block.data)

            step_run = block_updater.get_updated_step_run()
            db.add(step_run)
        except ValueError as e:
            raise HTTPException(status_code=404, detail=str(e)) from e

    # Create the interaction and associate it with the pre-loaded occurrence
    db_entity = Interaction(
        **create.model_dump(exclude_none=True),
        occurrence_id=occurrence_id,
        occurrence=occurrence,
    )
    db.add(db_entity)
    await db.commit()
    await db.refresh(db_entity)
    await db.flush()

    await InteractionStateChangeCascade(db_entity, db).run()

    await webhooks_service.notify_on_interaction(db_entity, request.path_params["agent_id"])

    return db_entity


@router.post(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/{step_name}",
    response_model=get_basemodel_from_table(Interaction, relationship_depth=0),
)
@inject
async def create_solve_manually_interaction(
    request: Request,
    create: InteractionCreate,
    workflow_name: str,
    workflow_execution_id: uuid.UUID,
    step_name: str,
    webhooks_service: WebhooksService = Depends(Provide[Container.webhooks_service]),
    db: AsyncSession = Depends(db_session),
):
    """Create a new solve manually interaction."""
    if create.kind != InteractionKindSolveManually:
        raise HTTPException(status_code=400, detail="Interaction kind must be solve manually")

    # Fetch the workflow execution
    workflow_execution = await db.scalar(
        select(WorkflowExecution)
        .where(
            WorkflowExecution.id == workflow_execution_id,
            WorkflowExecution.workflow.has(Workflow.name == workflow_name),
        )
        .options(joinedload(WorkflowExecution.workflow), joinedload(WorkflowExecution.step_runs))
    )

    if not workflow_execution:
        raise HTTPException(
            status_code=400, detail=f"Workflow execution {workflow_execution_id} does not exist"
        )

    # Fetch the step
    step = await db.scalar(
        select(Step)
        .where(
            Step.name == step_name,
            Step.workflow_id == workflow_execution.workflow_id,
            Step.solve_manually,
        )
        .options(joinedload(Step.inputs), joinedload(Step.outputs))
    )

    if not step:
        raise HTTPException(
            status_code=400,
            detail=(
                f"Step {step_name} does not exist in workflow {workflow_name}"
                f" or is not a solve manually step"
            ),
        )

    # Create the step run
    step_run = await create_step_run(
        create=StepRunCreate(
            step_id=step.id, status=StepRunStatusRunning, inputs=create.edited_data
        ),
        workflow_execution_id=workflow_execution_id,
        workflow_name=workflow_name,
    )

    # Create the occurrence
    occurrence = await create_occurrence(
        create=OccurrenceCreate(reason=OccurrenceReasonSolveManually, message="Solve manually"),
        workflow_name=workflow_name,
        workflow_execution_id=workflow_execution_id,
        step_run_id=step_run.id,
    )

    # Get the fully loaded occurrence
    loaded_occurrence = await db.scalar(
        select(Occurrence)
        .options(
            joinedload(Occurrence.step_run)
            .joinedload(StepRun.workflow_execution)
            .options(
                joinedload(WorkflowExecution.step_runs),
                joinedload(WorkflowExecution.workflow).joinedload(Workflow.steps),
            )
        )
        .where(Occurrence.id == occurrence.id)
    )

    # Create the interaction with the pre-loaded occurrence
    db_entity = Interaction(
        **create.model_dump(exclude_none=True),
        occurrence_id=occurrence.id,
        occurrence=loaded_occurrence,
    )
    db.add(db_entity)
    await db.commit()
    await db.refresh(db_entity)
    await db.flush()

    await InteractionStateChangeCascade(db_entity, db).run()

    await webhooks_service.notify_on_interaction(db_entity, request.path_params["agent_id"])

    return db_entity


@router.get(
    "/workflows",
    response_model=list[
        get_basemodel_from_table(
            Workflow,
            ignore_fields={
                "workflow.workflow_executions",
                "step.step_runs",
                "step.workflow",
            },
            relationship_depth=2,
        )
    ],
)
async def get_workflows(request: Request, db: AsyncSession = Depends(db_session)):
    """Get all workflows."""
    agent_id = request.path_params["agent_id"]
    workflows = await db.scalars(
        select(Workflow)
        .options(
            joinedload(Workflow.steps).options(
                joinedload(Step.inputs),
                joinedload(Step.outputs),
            ),
        )
        .where(Workflow.agent_id == agent_id)
    )
    return workflows.unique().all()


@router.get("/workflows/{workflow_name}", response_model=get_basemodel_from_table(Workflow))
async def get_workflow(
    request: Request, workflow_name: str, db: AsyncSession = Depends(db_session)
):
    """Get a workflow by name."""
    agent_id = request.path_params["agent_id"]
    workflow = await db.scalar(
        select(Workflow)
        .options(joinedload(Workflow.workflow_executions), joinedload(Workflow.steps))
        .where(Workflow.name == workflow_name, Workflow.agent_id == agent_id)
    )
    if workflow:
        return workflow
    else:
        raise HTTPException(status_code=404, detail=f"Workflow {workflow_name} not found")


@router.patch("/workflows/{workflow_name}", response_model=MessageResponse)
async def update_workflow(
    workflow_name: str, workflow: WorkflowUpdate, db: AsyncSession = Depends(db_session)
):
    """Update a workflow."""
    if not (workflow.name or workflow.description or workflow.agent_id or workflow.extra_fields):
        raise HTTPException(status_code=400, detail="At least 1 field to update required")

    result = await db.execute(
        update(Workflow)
        .where(Workflow.name == workflow_name)
        .values(**workflow.model_dump(exclude_none=True))
    )
    if result.rowcount == 0:
        raise HTTPException(status_code=404, detail=f"Workflow {workflow_name} not found")
    await db.commit()
    return MessageResponse(message="Workflow updated successfully")


@router.delete("/workflows/{workflow_name}", response_model=MessageResponse)
async def delete_workflow(workflow_name: str, db: AsyncSession = Depends(db_session)):
    """Delete a workflow."""
    result = await db.execute(delete(Workflow).where(Workflow.name == workflow_name))
    if result.rowcount == 0:
        raise HTTPException(status_code=404, detail=f"Workflow {workflow_name} not found")
    await db.commit()
    return MessageResponse(message="Workflow deleted successfully")


@router.get(
    "/workflows/{workflow_name}/steps",
    response_model=list[
        get_basemodel_from_table(Step, ignore_fields={"step.workflow", "step.step_runs"})
    ],
)
async def get_steps(
    response: Response,
    workflow_name: str,
    offset: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(db_session),
):
    """Get all steps for a workflow."""
    steps = await db.scalars(
        select(Step)
        .options(
            joinedload(Step.inputs),
            joinedload(Step.outputs),
        )
        .join(Step.workflow)
        .where(Workflow.name == workflow_name)
        .offset(offset)
        .limit(limit)
    )

    count = (
        await db.scalar(
            select(func.count(1))
            .select_from(Step)
            .join(Step.workflow)
            .where(Workflow.name == workflow_name)
        )
        or 0
    )
    response.headers["x-total-count"] = str(count)

    return steps.unique().all()


@router.patch(
    "/workflows/{workflow_name}/steps/{step_name}",
    response_model=get_basemodel_from_table(
        Step, relationship_depth=1, ignore_fields={"step.workflow", "step.step_runs"}
    ),
)
async def update_step(
    workflow_name: str, step_name: str, step: StepUpdate, db: AsyncSession = Depends(db_session)
):
    """Update a step for a workflow."""
    update_data = step.model_dump(exclude_none=True)
    if not update_data:
        raise HTTPException(status_code=400, detail="At least 1 field to update required")
    # First get the existing step to update
    existing_step = await db.scalar(
        select(Step)
        .where(Step.name == step_name)
        .options(joinedload(Step.inputs), joinedload(Step.outputs))
    )

    if not existing_step:
        raise HTTPException(status_code=404, detail=f"Step {step_name} not found")

    try:
        # Handle inputs - completely replace with new inputs
        if step.inputs is not None:
            # Clear the relationship before deleting
            existing_step.inputs = []
            await db.flush()

            # Then add the new inputs
            existing_step.inputs = [
                StepInput(**input_block.model_dump(exclude_none=True))
                for input_block in step.inputs
            ]
            db.add(existing_step)

        # Handle outputs - completely replace with new outputs
        if step.outputs is not None:
            # Clear the relationship before deleting
            existing_step.outputs = []
            await db.flush()

            # Then add the new outputs
            existing_step.outputs = [
                StepOutput(**output_block.model_dump(exclude_none=True))
                for output_block in step.outputs
            ]
            db.add(existing_step)

        # Update the remaining fields
        update_fields = {k: v for k, v in update_data.items() if k not in ["inputs", "outputs"]}
        if update_fields:
            existing_step = await db.scalar(
                update(Step)
                .where(Step.id == existing_step.id)
                .values(**update_fields)
                .returning(Step)
            )

        await db.commit()

        # Refresh the step with all relationships loaded
        await db.refresh(existing_step, ["inputs", "outputs"])
        return existing_step

    except StepInputValidationError as e:
        raise HTTPException(status_code=422, detail=str(e)) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.delete("/workflows/{workflow_name}/steps/{step_name}", response_model=MessageResponse)
async def delete_step(step_name: str, db: AsyncSession = Depends(db_session)):
    """Delete a step for a workflow."""
    result = await db.execute(delete(Step).where(Step.name == step_name))
    if result.rowcount == 0:
        raise HTTPException(status_code=404, detail=f"Step {step_name} not found")
    await db.commit()
    return MessageResponse(message="Step deleted successfully")


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}",
    response_model=get_basemodel_from_table(
        WorkflowExecution,
        relationship_depth=5,
        ignore_fields={
            "workflow_execution.history",
            "workflow_execution.step_runs",
            "workflow.workflow_executions",
            "step_run.workflow_execution",
            "step_run.step",
            "step.workflow",
            "occurrence.step_run",
            "step_input.step",
            "step_output.step",
        },
    ),
)
async def get_workflow_execution(
    workflow_name: str, workflow_execution_id: uuid.UUID, db: AsyncSession = Depends(db_session)
):
    """Get a workflow execution by name and ID."""
    # Get the workflow execution with workflow and its relationships
    workflow_execution = await db.scalar(
        select(WorkflowExecution)
        .where(WorkflowExecution.id == workflow_execution_id)
        .options(
            # Load workflow with its relationships
            selectinload(WorkflowExecution.workflow).options(
                selectinload(Workflow.steps).options(
                    selectinload(Step.inputs),
                    selectinload(Step.outputs),
                    noload(Step.step_runs),  # Don't load step runs here, we'll load them separately
                )
            )
        )
    )

    if not workflow_execution:
        raise HTTPException(
            status_code=404,
            detail=f"Workflow execution {workflow_execution_id} not found",
        )

    # Create a CTE for the latest step runs
    latest_step_runs = (
        select(StepRun)
        .where(StepRun.workflow_execution_id == workflow_execution_id)
        .order_by(
            StepRun.step_id,
            StepRun.started_at.desc(),
        )
        .distinct(StepRun.step_id)
        .cte("latest_step_runs")
    )

    # Get the step runs with their relationships
    step_runs = await db.scalars(
        select(StepRun)
        .join(latest_step_runs, StepRun.id == latest_step_runs.c.id)
        .options(
            selectinload(StepRun.occurrences).options(selectinload(Occurrence.interactions)),
        )
    )
    step_runs = step_runs.all()

    # Create a mapping of step_id to step_run
    step_run_map = {step_run.step_id: step_run for step_run in step_runs}

    # Attach step runs to their corresponding steps
    for step in workflow_execution.workflow.steps:
        step.step_runs = [step_run_map[step.id]] if step.id in step_run_map else []

    return workflow_execution


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/history",
    response_model=list[get_basemodel_from_table(WorkflowExecutionHistory, relationship_depth=0)],
)
async def get_workflow_execution_history(
    workflow_name: str, workflow_execution_id: uuid.UUID, db: AsyncSession = Depends(db_session)
):
    """Get the history of a workflow execution."""
    history = await db.scalars(
        select(WorkflowExecutionHistory)
        .options(joinedload(WorkflowExecutionHistory.workflow_execution))
        .where(WorkflowExecutionHistory.workflow_execution_id == workflow_execution_id)
        .order_by(WorkflowExecutionHistory.created_at)
    )
    return history.unique().all()


@router.get(
    "/workflows/{workflow_name}/workflow-executions",
    response_model=list[
        get_basemodel_from_table(
            WorkflowExecution,
            ignore_fields={
                "workflow_execution.workflow",
                "workflow_execution.step_runs",
                "workflow_execution.history",
            },
        )
    ],
)
async def get_workflow_executions(
    response: Response,
    workflow_name: str,
    offset: int = 0,
    limit: int = 10,
    search: str = "",
    search_column: Literal["extra_fields", "status", "id"] | None = None,
    filter_: Literal["open", "closed"] | None = None,
    filter_status: WorkflowExecutionStatus | Literal["blocked"] | None = None,
    test_execution: bool = False,
    db: AsyncSession = Depends(db_session),
):
    """Get all workflow executions for a workflow."""
    base_query = (
        select(WorkflowExecution)
        .join(WorkflowExecution.workflow)
        .where(Workflow.name == workflow_name)
        .where(WorkflowExecution.test_execution == test_execution)
        .options(
            joinedload(WorkflowExecution.workflow),
            joinedload(WorkflowExecution.history),
        )
    )

    if filter_ == "closed":
        base_query = base_query.where(WorkflowExecution.status.in_(WorkflowTerminalStatuses))
    elif filter_ == "open":
        base_query = base_query.where(WorkflowExecution.status.notin_(WorkflowTerminalStatuses))

    if filter_status:
        filtering_statuses = (
            [filter_status] if "blocked" not in filter_status else WorkflowExecutionStatusBlocked
        )
        base_query = base_query.where(WorkflowExecution.status.in_(filtering_statuses))

    if search:
        if search_column:
            if search_column == "id":
                base_query = base_query.where(
                    WorkflowExecution.id.cast(String).ilike(f"%{search}%")
                )
            elif search_column == "status":
                base_query = base_query.where(WorkflowExecution.status.ilike(f"%{search}%"))
            elif search_column == "extra_fields":
                base_query = base_query.where(
                    WorkflowExecution.extra_fields.cast(String).ilike(f"%{search}%")
                )
        else:
            base_query = base_query.where(
                or_(
                    WorkflowExecution.id.cast(String).ilike(f"%{search}%"),
                    WorkflowExecution.status.ilike(f"%{search}%"),
                    WorkflowExecution.extra_fields.cast(String).ilike(f"%{search}%"),
                )
            )

    workflow_executions = await db.scalars(
        base_query.options(
            joinedload(WorkflowExecution.workflow),
        )
        .offset(offset)
        .limit(limit)
    )

    # Get counts by status for executions of this workflow, filtered only by open/closed
    count_query = (
        select(WorkflowExecution.status, func.count().label("count"))
        .join(WorkflowExecution.workflow)
        .where(Workflow.name == workflow_name)
        .where(WorkflowExecution.test_execution == test_execution)
    )

    # Apply only the open/closed filter to the count query
    if filter_ == "closed":
        count_query = count_query.where(WorkflowExecution.status.in_(WorkflowTerminalStatuses))
    elif filter_ == "open":
        count_query = count_query.where(WorkflowExecution.status.notin_(WorkflowTerminalStatuses))

    count_query = count_query.group_by(WorkflowExecution.status)
    status_counts = await db.execute(count_query)

    # Convert to dictionary and add total count
    status_count_dict = {row["status"]: row["count"] for row in status_counts.mappings().all()}
    status_count_dict["all"] = sum(status_count_dict.values())
    response.headers["x-count-by-status"] = str(status_count_dict)

    return workflow_executions.unique().all()


@router.patch(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}",
    response_model=MessageResponse,
)
async def update_workflow_execution(
    workflow_execution_id: uuid.UUID,
    data: WorkflowExecutionUpdate,
    db: AsyncSession = Depends(db_session),
):
    """Update a workflow execution."""
    if not (data.status or data.extra_fields):
        raise HTTPException(status_code=400, detail="At least 1 field to update required")

    result = await db.execute(
        update(WorkflowExecution)
        .where(WorkflowExecution.id == workflow_execution_id)
        .values(**data.model_dump(exclude_none=True))
    )
    if result.rowcount == 0:
        raise HTTPException(
            status_code=404,
            detail=f"Workflow Execution {workflow_execution_id} not found",
        )
    await db.commit()
    return MessageResponse(message="Workflow Execution updated successfully")


@router.delete(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}",
    response_model=MessageResponse,
)
async def delete_workflow_execution(
    workflow_execution_id: uuid.UUID, db: AsyncSession = Depends(db_session)
):
    """Delete a workflow execution."""
    result = await db.execute(
        delete(WorkflowExecution).where(WorkflowExecution.id == workflow_execution_id)
    )
    if result.rowcount == 0:
        raise HTTPException(
            status_code=404,
            detail=f"Workflow Execution {workflow_execution_id} not found",
        )
    await db.commit()
    return MessageResponse(message="Workflow Execution deleted successfully")


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs/{step_run_id}",
    response_model=get_basemodel_from_table(
        StepRun,
        relationship_depth=2,
        ignore_fields={
            "step_run.workflow_execution",
            "step.step_runs",
            "occurrence.step_run",
        },
    ),
)
async def get_step_run(step_run_id: uuid.UUID, db: AsyncSession = Depends(db_session)):
    """Get a step run by ID."""
    step_run = await db.scalar(
        select(StepRun)
        .options(
            joinedload(StepRun.workflow_execution),
            joinedload(StepRun.step).options(
                joinedload(Step.workflow),
                joinedload(Step.inputs),
                joinedload(Step.outputs),
            ),
            joinedload(StepRun.occurrences).options(
                joinedload(Occurrence.step_run), joinedload(Occurrence.interactions)
            ),
        )
        .where(StepRun.id == step_run_id)
    )
    if step_run:
        return step_run
    else:
        raise HTTPException(
            status_code=404,
            detail=f"Step Run {step_run_id} not found",
        )


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs",
    response_model=list[
        get_basemodel_from_table(
            StepRun,
            relationship_depth=2,
            ignore_fields={
                "step_run.workflow_execution",
                "step.workflow",
                "step.step_runs",
                "occurrence.step_run",
            },
        )
    ],
)
async def get_step_runs(
    response: Response,
    workflow_name: str,
    workflow_execution_id: uuid.UUID,
    offset: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(db_session),
):
    """Get all step runs for a workflow execution."""
    step_runs = await db.scalars(
        select(StepRun)
        .options(
            joinedload(StepRun.workflow_execution),
            joinedload(StepRun.step).options(
                joinedload(Step.inputs),
                joinedload(Step.outputs),
            ),
            joinedload(StepRun.occurrences).options(joinedload(Occurrence.interactions)),
        )
        .where(StepRun.workflow_execution_id == workflow_execution_id)
        .offset(offset)
        .limit(limit)
    )

    count = (
        await db.scalar(
            select(func.count(1))
            .select_from(StepRun)
            .where(StepRun.workflow_execution_id == workflow_execution_id)
        )
        or 0
    )
    response.headers["x-total-count"] = str(count)

    return step_runs.unique().all()


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs/by-step-name/{step_name}",
    response_model=get_basemodel_from_table(StepRun, relationship_depth=0),
)
async def get_step_run_by_step_name(
    workflow_execution_id: uuid.UUID, step_name: str, db: AsyncSession = Depends(db_session)
):
    """Get a step run by workflow execution ID and step name."""
    step_run = await db.scalar(
        select(StepRun)
        .join(Step, StepRun.step_id == Step.id)
        .options(
            joinedload(StepRun.workflow_execution),
            joinedload(StepRun.step),
            joinedload(StepRun.occurrences),
        )
        .where(
            StepRun.workflow_execution_id == workflow_execution_id,
            Step.name == step_name,
        )
    )

    if step_run:
        return step_run
    else:
        raise HTTPException(
            status_code=404,
            detail=f"Step run not found for workflow execution {workflow_execution_id} and \
            step name {step_name}",
        )


@router.patch(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs/{step_run_id}",
    response_model=get_basemodel_from_table(
        StepRun,
        relationship_depth=1,
        ignore_fields={"step_run.workflow_execution", "step_run.step"},
    ),
)
async def update_step_run(
    step_run_id: uuid.UUID, data: StepRunUpdate, db: AsyncSession = Depends(db_session)
):
    """Update a step run."""
    if not data.occurrence and not (data.status or data.outputs):
        raise HTTPException(status_code=400, detail="At least 1 field to update required")
    if data.status in StepRunStatusBlocked + [StepRunStatusError]:
        raise HTTPException(
            status_code=400,
            detail="Blocked and error status must be set via occurrence",
        )

    # First get the step run to validate against
    step_run_for_validation = await db.scalar(
        select(StepRun)
        .options(
            joinedload(StepRun.workflow_execution),
            joinedload(StepRun.step).options(joinedload(Step.inputs), joinedload(Step.outputs)),
        )
        .where(StepRun.id == step_run_id)
    )

    if not step_run_for_validation:
        raise HTTPException(
            status_code=404,
            detail=f"Step Run {step_run_id} not found",
        )

    if step_run_for_validation.workflow_execution.status in WorkflowTerminalStatuses:
        raise HTTPException(
            status_code=400,
            detail=(
                f"Cannot update step run: Workflow execution "
                f"{step_run_for_validation.workflow_execution_id} "
                f"is in terminal status "
                f"{step_run_for_validation.workflow_execution.status}"
            ),
        )

    # Validate StepRun against Step structure definition
    StepRunValidator(step_run_for_validation.step, data).validate()

    # Update the step run using ORM
    update_data = data.model_dump(exclude_none=True, exclude={"occurrence"})

    # Only attempt update if there is data to update
    step_run = step_run_for_validation
    if update_data:
        step_run = await db.scalar(
            update(StepRun)
            .where(StepRun.id == step_run_id)
            .values(**update_data)
            .returning(StepRun)
        )

    # Create a new occurrence
    if data.occurrence:
        occurrence = Occurrence(
            step_run_id=step_run_id,
            reason=data.occurrence.reason,
            status=OccurrenceStatusOpen,
            message=data.occurrence.message,
        )
        db.add(occurrence)

    await db.commit()

    # Eagerly load all relationships needed for the cascade
    await db.refresh(step_run, ["workflow_execution", "step", "occurrences"])
    await db.refresh(step_run.workflow_execution, ["workflow", "step_runs"])
    await db.refresh(step_run.workflow_execution.workflow, ["steps"])
    await db.refresh(step_run.step, ["inputs", "outputs"])

    await db.flush()

    if not data.occurrence:
        # If an occurrence was created, it can only be of type "run_from_previous_step"
        # which means we don't want any state change cascade to happen
        await StepRunStateChangeCascade(step_run, db).run()

    return step_run


@router.delete(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs/{step_run_id}",
    response_model=MessageResponse,
)
async def delete_step_run(step_run_id: uuid.UUID, db: AsyncSession = Depends(db_session)):
    """Delete a step run."""
    step_run = await db.scalar(
        select(StepRun)
        .options(joinedload(StepRun.workflow_execution))
        .where(StepRun.id == step_run_id)
    )

    if not step_run:
        raise HTTPException(
            status_code=404,
            detail=f"Step Run {step_run_id} not found",
        )

    if step_run.workflow_execution.status in WorkflowTerminalStatuses:
        raise HTTPException(
            status_code=400,
            detail=(
                f"Cannot delete step run: Workflow execution {step_run.workflow_execution_id} "
                f"is in terminal status {step_run.workflow_execution.status}"
            ),
        )

    await db.delete(step_run)
    await db.commit()
    return MessageResponse(message="Step Run deleted successfully")


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}/step-runs/{step_run_id}/occurrences/{occurrence_id}",
    response_model=get_basemodel_from_table(Occurrence, relationship_depth=0),
)
async def get_occurrence(occurrence_id: uuid.UUID, db: AsyncSession = Depends(db_session)):
    """Get an occurrence by ID."""
    occurrence = await db.scalar(select(Occurrence).where(Occurrence.id == occurrence_id))
    if occurrence:
        return occurrence
    else:
        raise HTTPException(
            status_code=404,
            detail=f"Occurrence {occurrence_id} not found",
        )


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}"
    "/step-runs/{step_run_id}/occurrences",
    response_model=list[
        get_basemodel_from_table(Occurrence, ignore_fields={"occurrence.step_run"})
    ],
)
async def get_occurrences(
    response: Response,
    workflow_name: str,
    workflow_execution_id: uuid.UUID,
    step_run_id: uuid.UUID,
    offset: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(db_session),
):
    """Get all occurrences for a step run."""
    occurrences = await db.scalars(
        select(Occurrence)
        .options(joinedload(Occurrence.interactions), joinedload(Occurrence.step_run))
        .where(Occurrence.step_run_id == step_run_id)
        .offset(offset)
        .limit(limit)
    )
    o = occurrences.unique().all()
    count = (
        await db.scalar(
            select(func.count(1))
            .select_from(Occurrence)
            .where(Occurrence.step_run_id == step_run_id)
        )
        or 0
    )
    response.headers["x-total-count"] = str(count)
    return o


@router.patch(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}"
    "/step-runs/{step_run_id}/occurrences/{occurrence_id}",
    response_model=MessageResponse,
)
async def update_occurrence(
    occurrence_id: uuid.UUID, data: OccurrenceUpdate, db: AsyncSession = Depends(db_session)
):
    """Update an occurrence."""
    if not (data.reason or data.status):
        raise HTTPException(status_code=400, detail="At least 1 field to update required")

    # Update the occurrence using ORM
    update_data = data.model_dump(exclude_none=True)
    occurrence = await db.scalar(
        update(Occurrence)
        .where(Occurrence.id == occurrence_id)
        .values(**update_data)
        .returning(Occurrence)
        .options(joinedload(Occurrence.step_run).joinedload(StepRun.workflow_execution))
    )

    if not occurrence:
        raise HTTPException(
            status_code=404,
            detail=f"Occurrence {occurrence_id} not found",
        )

    await db.commit()
    await db.flush()

    await OccurrenceStateChangeCascade(occurrence, db).run()

    return MessageResponse(message="Occurrence updated successfully")


@router.delete(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}"
    "/step-runs/{step_run_id}/occurrences/{occurrence_id}",
    response_model=MessageResponse,
)
async def delete_occurrence(occurrence_id: uuid.UUID, db: AsyncSession = Depends(db_session)):
    """Delete an occurrence."""
    result = await db.execute(delete(Occurrence).where(Occurrence.id == occurrence_id))
    if result.rowcount == 0:
        raise HTTPException(
            status_code=404,
            detail=f"Occurrence {occurrence_id} not found",
        )
    await db.commit()
    return MessageResponse(message="Occurrence deleted successfully")


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}"
    "/step-runs/{step_run_id}/occurrences/{occurrence_id}/interactions/{interaction_id}",
    response_model=get_basemodel_from_table(Interaction),
)
async def get_interaction(interaction_id: uuid.UUID, db: AsyncSession = Depends(db_session)):
    """Get an interaction by ID."""
    interaction = await db.scalar(
        select(Interaction)
        .options(
            joinedload(Interaction.occurrence)
            .joinedload(Occurrence.step_run)
            .joinedload(StepRun.workflow_execution)
            .options(
                joinedload(WorkflowExecution.workflow).joinedload(Workflow.steps),
                joinedload(WorkflowExecution.step_runs),
            )
        )
        .where(Interaction.id == interaction_id)
    )
    if interaction:
        return interaction
    else:
        raise HTTPException(
            status_code=404,
            detail=f"Interaction {interaction_id} not found",
        )


@router.get(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}"
    "/step-runs/{step_run_id}/occurrences/{occurrence_id}/interactions",
    response_model=list[get_basemodel_from_table(Interaction)],
)
async def get_interactions(
    response: Response,
    workflow_name: str,
    workflow_execution_id: uuid.UUID,
    step_run_id: uuid.UUID,
    occurrence_id: uuid.UUID,
    offset: int = 0,
    limit: int = 10,
    db: AsyncSession = Depends(db_session),
):
    """Get all interactions for an occurrence."""
    interactions = await db.scalars(
        select(Interaction)
        .join(Occurrence)
        .options(joinedload(Interaction.occurrence))
        .where(Occurrence.id == occurrence_id)
        .limit(limit)
        .offset(offset)
    )
    count = (
        await db.scalar(
            select(func.count(1))
            .select_from(Interaction)
            .where(Interaction.occurrence_id == occurrence_id)
        )
        or 0
    )
    response.headers["x-total-count"] = str(count)
    return interactions.all()


@router.patch(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}"
    "/step-runs/{step_run_id}/occurrences/{occurrence_id}/interactions/{interaction_id}",
    response_model=MessageResponse,
)
@inject
async def update_interaction(
    interaction_id: uuid.UUID,
    data: InteractionUpdate,
    webhooks_service: WebhooksService = Depends(Provide[Container.webhooks_service]),
    db: AsyncSession = Depends(db_session),
):
    """Update an interaction."""
    if not (data.edited_data or data.kind):
        raise HTTPException(status_code=400, detail="At least 1 field to update required")

    # Update the interaction using ORM
    update_data = data.model_dump(exclude_none=True)
    interaction = await db.scalar(
        update(Interaction)
        .where(Interaction.id == interaction_id)
        .values(**update_data)
        .returning(Interaction)
        .options(
            joinedload(Interaction.occurrence)
            .joinedload(Occurrence.step_run)
            .joinedload(StepRun.workflow_execution)
            .options(
                joinedload(WorkflowExecution.workflow).joinedload(Workflow.steps),
                joinedload(WorkflowExecution.step_runs),
            )
        )
    )

    if not interaction:
        raise HTTPException(
            status_code=404,
            detail=f"Interaction {interaction_id} not found",
        )

    # Update the step run with the edited data
    if data.edited_data:
        try:
            block_updater = StepRunBlockUpdater(interaction.occurrence.step_run)
            for block in data.edited_data:
                block_updater.update_output(block.step_block_name, block.data)

            step_run = block_updater.get_updated_step_run()
            db.add(step_run)
        except ValueError as e:
            raise HTTPException(status_code=404, detail=str(e)) from e

    await db.commit()
    await db.flush()

    await InteractionStateChangeCascade(interaction, db).run()

    await webhooks_service.notify_on_interaction(interaction)

    return MessageResponse(message="Interaction updated successfully")


@router.delete(
    "/workflows/{workflow_name}/workflow-executions/{workflow_execution_id}"
    "/step-runs/{step_run_id}/occurrences/{occurrence_id}/interactions/{interaction_id}",
    response_model=MessageResponse,
)
async def delete_interaction(interaction_id: uuid.UUID, db: AsyncSession = Depends(db_session)):
    """Delete an interaction."""
    result = await db.execute(delete(Interaction).where(Interaction.id == interaction_id))
    if result.rowcount == 0:
        raise HTTPException(
            status_code=404,
            detail=f"Interaction {interaction_id} not found",
        )
    await db.commit()
    return MessageResponse(message="Interaction deleted successfully")


@router.post("/files", response_model=FileUploadResponse)
@inject
async def create_file(
    request: FileUploadRequest,
    storage_service: StorageInterface = Depends(Provide[Container.storage_service]),
    db: AsyncSession = Depends(db_session),
):
    """Generate a signed URL for uploading a file."""
    # Create file mapping first to get the auto-generated UUID
    file_mapping = File(
        storage_path="",  # Temporary empty path, will be updated after we get the ID
        file_name=request.file_name,  # Store the original file name
    )
    db.add(file_mapping)
    await db.commit()
    await db.refresh(file_mapping)

    # Get the storage path using the storage service
    storage_path = storage_service.get_storage_path(
        file_id=file_mapping.id, file_name=request.file_name
    )

    # Update storage path in database
    file_mapping.storage_path = storage_path
    await db.commit()
    await db.refresh(file_mapping)

    try:
        # Generate upload URL using the storage service
        file_metadata = FileMetadata(
            id=file_mapping.id,
            file_name=request.file_name,
            storage_path=file_mapping.storage_path,
            content_type=request.content_type,
        )
        upload_info = await storage_service.generate_upload_url(file_metadata)

        return FileUploadResponse(file_id=upload_info.file_id, upload_url=upload_info.upload_url)

    except Exception as e:
        # If there's an error generating the signed URL, clean up the database entry
        await db.delete(file_mapping)
        await db.commit()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate upload URL: {str(e)}",
        ) from e


@router.get("/files/{file_id}", response_model=FileDownloadResponse)
@inject
async def get_file(
    file_id: uuid.UUID,
    storage_service: StorageInterface = Depends(Provide[Container.storage_service]),
    db: AsyncSession = Depends(db_session),
):
    """Get a signed URL for viewing a file."""
    file = await db.scalar(select(File).where(File.id == file_id))
    if not file:
        raise HTTPException(
            status_code=404,
            detail=f"File {file_id} not found",
        )

    # Generate signed URL using the storage service
    file_metadata = FileMetadata(
        id=file.id,
        file_name=file.file_name,
        storage_path=file.storage_path,
    )

    download_info = await storage_service.generate_download_url(file_metadata)

    return FileDownloadResponse(
        id=download_info.file_id,
        url=download_info.download_url,
        file_name=download_info.file_name,
    )
