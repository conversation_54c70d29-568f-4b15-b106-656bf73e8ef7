"""Pydantic schemas for global issues management API."""

import uuid
from datetime import datetime
from typing import Any, Literal

from pydantic import BaseModel, Field, field_validator

from gen_os_am_core.models.enums import IssueCategory, IssueState

from .common import PaginationDetails, PaginationFilters


class IssueListFilters(PaginationFilters):
    """Query string filters for listing issues."""

    is_open: bool = Field(
        True,
        description="""If true, return only open issues and include summary;
        if false, return only closed issues without summary.""",
    )
    search: str = ""
    search_column: Literal["id", "description", "issue_type", "severity"] | None = None

    @field_validator("sort_field")
    @classmethod
    def _validate_sort_field(cls, v: str) -> str:
        allowed = {"created_at", "updated_at", "incidents"}
        if v not in allowed:
            raise ValueError(
                f"Invalid sort_field. Allowed values are: {', '.join(allowed)}"
            )
        return v


class IssueListItem(BaseModel):
    """Simplified schema for issue rows in list endpoints."""

    id: uuid.UUID
    description: str
    issue_type: IssueCategory
    state: IssueState
    agent_name: str | None = None
    severity: str | None = None
    incidents: int
    created_at: datetime
    updated_at: datetime


class PaginatedIssuesResponse(BaseModel):
    """Standard paginated response for issue list endpoint."""

    items: list[IssueListItem]
    pagination: PaginationDetails
    summary: dict[str, Any] | None = None
