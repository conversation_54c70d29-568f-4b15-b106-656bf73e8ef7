"""API wrapper for the Agent Manager (AM).

This module provides a class-based wrapper for the FastAPI application that
dynamically aggregates the APIs from the Knowledge Management (KM) and
Case Management (CM) submodules if they are available in the environment.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from gen_os_am_core.api.routers.agent_config import AgentConfigRouter
from gen_os_am_core.api.routers.agent_issues import AgentIssuesRouter
from gen_os_am_core.api.routers.agents import AgentsRouter
from gen_os_am_core.api.routers.global_issues import GlobalIssuesRouter
from gen_os_am_core.api.routers.health import HealthRouter
from gen_os_am_core.middleware import AgentValidationMiddleware
from gen_os_am_core.services import (
    services_availability_check,
    services_database_check,
    services_router_inclusion,
)
from gen_os_am_core.settings import Settings
from gen_os_am_core.workflows.router import WorkflowRouter


class AgentManagerAPI:
    """FastAPI wrapper for the Agent Manager backend API.

    This class provides a factory for creating a FastAPI application with all the
    necessary endpoints and middleware configuration for the agent manager system.
    """

    def __init__(
        self,
        allow_origins: list[str] | None = None,
        allow_methods: list[str] | None = None,
        allow_headers: list[str] | None = None,
        allow_credentials: bool = True,
        expose_headers: list[str] | None = None,
        logger: logging.Logger | None = None,
    ):
        """Initialize the API with CORS configuration.

        Args:
            allow_origins: List of allowed origins for CORS. Defaults to ["*"].
            allow_methods: List of allowed HTTP methods. Defaults to ["*"].
            allow_headers: List of allowed headers. Defaults to ["*"].
            allow_credentials: Whether to allow credentials. Defaults to True.
            expose_headers: List of headers to expose. Defaults to ["*"].
            logger: Optional logger instance.
                If not provided, will use the default logger.

        """
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["*"]
        self.allow_headers = allow_headers or ["*"]
        self.allow_credentials = allow_credentials
        self.expose_headers = expose_headers or ["*"]
        self.logger = logger or logging.getLogger(__name__)
        self.settings = Settings.get_settings()

    async def _check_database_access(self) -> dict[str, bool]:
        """Check database access."""
        return await services_database_check()

    async def _check_services_access(self) -> dict[str, bool]:
        """Check services access."""
        return services_availability_check()

    @asynccontextmanager
    async def lifespan(self, app: FastAPI):
        """Lifespan context manager for FastAPI application.

        Args:
            app: FastAPI application instance

        """
        self.logger.info("Agent Manager API starting up...")
        yield
        self.logger.info("Agent Manager API shutting down...")

    def create_api(self) -> FastAPI:
        """Create and configure the FastAPI application.

        Returns:
            FastAPI: The configured FastAPI application

        """
        app = FastAPI(
            title="Agent Manager API",
            description="Aggregated API for Knowledge Management and Case Management",
            lifespan=self.lifespan,
        )
        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=self.allow_origins,
            allow_credentials=self.allow_credentials,
            allow_methods=self.allow_methods,
            allow_headers=self.allow_headers,
            expose_headers=self.expose_headers,
        )

        # Set up routers

        # Health router
        health_router = HealthRouter(
            database_checker=self._check_database_access,
            services_checker=self._check_services_access,
            settings=self.settings,
        )
        app.include_router(health_router.get_router())
        self.logger.info("Successfully included Health routes")
        # Agents router
        agents_router = AgentsRouter(logger=self.logger)
        app.include_router(agents_router.get_router())
        self.logger.info("Successfully included Agents routes")
        # Include Agent Configuration router
        agent_config_router = AgentConfigRouter()
        app.include_router(agent_config_router.get_router())
        self.logger.info("Successfully included Agent Configuration routes")
        # Workflow orchestration router
        # TODO(Henrique): This router should only be included
        # if the workflow module is available
        workflow_router = WorkflowRouter()
        app.include_router(workflow_router.get_router())
        self.logger.info("Successfully included Workflow routes")

        # Include Agent Issues router (core functionality)
        agent_issues_router = AgentIssuesRouter()
        app.include_router(agent_issues_router.get_router())
        self.logger.info("Successfully included Agent Issues routes")

        # Include Global Issues router
        global_issues_router = GlobalIssuesRouter()
        app.include_router(global_issues_router.get_router())
        self.logger.info("Successfully included Global Issues routes")

        # Dynamic Router Inclusion - KM, CM, and Conversational services
        services_router_inclusion(app, self.logger)
        # Add agent validation middleware after all routers are included
        app.add_middleware(AgentValidationMiddleware, logger=self.logger)
        return app
