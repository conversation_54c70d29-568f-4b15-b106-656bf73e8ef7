"""Pydantic schemas for agent configuration management API."""

from datetime import datetime

from pydantic import BaseModel, ConfigDict, Field


class AgentConfigurationBase(BaseModel):
    """Base schema for agent configuration with common fields."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    name: str = Field(..., description="Configuration parameter name")
    description: str | None = Field(None, description="Human-readable description")
    value: str = Field(..., description="Configuration value")


class AgentConfigurationItem(BaseModel):
    """Schema for a single agent configuration item in sync operations."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    id: str = Field(..., description="Unique identifier of the configuration")
    name: str = Field(..., description="Configuration parameter name")
    description: str | None = Field(None, description="Human-readable description")
    value: str = Field(..., description="Configuration value")


class AgentConfigurationSyncRequest(BaseModel):
    """Schema for synchronizing agent configurations."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    configurations: list[AgentConfigurationItem] = Field(
        ..., description="List of agent configurations to synchronize"
    )


class AgentConfigurationResponse(AgentConfigurationBase):
    """Schema for returning agent configuration data."""

    id: str = Field(..., description="Unique identifier of the configuration")
    agent_id: str = Field(
        ..., description="ID of the agent this configuration belongs to"
    )
    created_at: datetime = Field(..., description="When this configuration was created")
    updated_at: datetime = Field(
        ..., description="When this configuration was last updated"
    )
