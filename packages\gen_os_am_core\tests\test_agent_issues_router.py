"""Tests for the agent issues router."""

import uuid

import pytest
import pytest_asyncio
from gen_os_sdk_emulator.core.database.session import SessionManager
from sqlalchemy import select

from gen_os_am_core.database.models.agent_issue import (
    Issue,
    ReportedIncidentConversation,
)
from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.settings import DatabaseSettings

db_settings = DatabaseSettings.get_settings()


class TestAgentIssuesRouter:
    """Test cases for the agent issues router."""

    @pytest.fixture
    def test_agent_id(self):
        """Test agent ID fixture."""
        return "test-agent-issues-456"

    @pytest.fixture
    def test_user_id(self):
        """Test user ID for created_by field."""
        return "test-user-789"

    @pytest_asyncio.fixture
    async def create_test_agent(self, test_agent_id):
        """Create a test agent in the database."""
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent",
                description="Test agent for issues router tests",
            )
            db.add(agent)
            await db.commit()
            await db.refresh(agent)

            yield agent

            # Cleanup
            await db.delete(agent)
            await db.commit()

    @pytest.fixture
    def conversation_incident_data(self):
        """Sample conversation incident data."""
        return {
            "conversation_id": str(uuid.uuid4()),
            "answer": "This is a sample answer from a conversation.",
            "description": "Description of the conversation incident.",
            "severity": "high",
        }

    @pytest.fixture
    def workflow_incident_data(self):
        """Sample workflow incident data."""
        return {
            "workflow_execution_id": str(uuid.uuid4()),
            "step": "step_name_example",
            "description": "Description of the workflow incident.",
            "severity": "medium",
        }

    @pytest.fixture
    def issue_create_request_data(
        self, conversation_incident_data, workflow_incident_data
    ):
        """Sample issue creation request data."""
        return {
            "description": "A test issue description.",
            "issue_type": "GenAI",
            "state": "open",
            "reported_incidents_conversations": [conversation_incident_data],
            "reported_incidents_workflows": [workflow_incident_data],
        }

    @pytest_asyncio.fixture
    async def create_test_issue(
        self,
        test_agent_id,
        issue_create_request_data,
        test_user_id,
        create_test_agent,
    ):
        """Create a test issue with incidents for testing get/update/delete."""
        async with SessionManager.get_session() as db:
            issue = Issue(
                agent_id=test_agent_id,
                description=issue_create_request_data["description"],
                issue_type=issue_create_request_data["issue_type"],
                state=issue_create_request_data["state"],
                deleted=False,
            )

            for conv_data in issue_create_request_data[
                "reported_incidents_conversations"
            ]:
                issue.reported_incidents_conversations.append(
                    ReportedIncidentConversation(**conv_data, created_by=test_user_id)
                )

            for wf_data in issue_create_request_data["reported_incidents_workflows"]:
                issue.reported_incidents_workflows.append(
                    ReportedIncidentWorkflow(**wf_data, created_by=test_user_id)
                )

            db.add(issue)
            await db.commit()
            await db.refresh(
                issue,
                ["reported_incidents_conversations", "reported_incidents_workflows"],
            )
            yield issue

    @pytest.mark.asyncio
    async def test_create_issue_success(
        self,
        client,
        test_agent_id,
        test_user_id,
        issue_create_request_data,
        create_test_agent,
    ):
        """Test successful creation of an issue with all related data."""
        headers = {"X-User-ID": test_user_id}
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue",
            json=issue_create_request_data,
            headers=headers,
        )

        assert response.status_code == 201
        data = response.json()

        # Verify response body
        assert data["description"] == issue_create_request_data["description"]
        assert data["agent_id"] == test_agent_id
        assert data["state"] == "open"
        assert len(data["reported_incidents_conversations"]) == 1
        assert len(data["reported_incidents_workflows"]) == 1

        conv_incident = data["reported_incidents_conversations"][0]
        assert conv_incident["created_by"] == test_user_id
        assert (
            conv_incident["conversation_id"]
            == issue_create_request_data["reported_incidents_conversations"][0][
                "conversation_id"
            ]
        )

        # Verify database state
        async with SessionManager.get_session(db_settings) as db:
            # Convert string UUID to UUID object for database query
            issue_id = uuid.UUID(data["id"])
            stmt = select(Issue).where(Issue.id == issue_id)
            result = await db.execute(stmt)
            issue_in_db = result.scalar_one_or_none()

            assert issue_in_db is not None
            assert issue_in_db.agent_id == test_agent_id
            assert issue_in_db.description == issue_create_request_data["description"]

            # Verify relationships were created
            await db.refresh(
                issue_in_db,
                ["reported_incidents_conversations", "reported_incidents_workflows"],
            )
            assert len(issue_in_db.reported_incidents_conversations) == 1
            assert len(issue_in_db.reported_incidents_workflows) == 1
            assert (
                issue_in_db.reported_incidents_conversations[0].created_by
                == test_user_id
            )

    @pytest.mark.asyncio
    async def test_create_issue_missing_user_id_header(
        self, client, test_agent_id, issue_create_request_data, create_test_agent
    ):
        """Test issue creation when the X-User-ID header is missing."""
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue", json=issue_create_request_data
        )

        assert response.status_code == 201
        data = response.json()
        assert data["reported_incidents_conversations"][0]["created_by"] is None
        assert data["reported_incidents_workflows"][0]["created_by"] is None

        # Verify database state
        async with SessionManager.get_session(db_settings) as db:
            # Convert string UUID to UUID object for database query
            issue_id = uuid.UUID(data["id"])
            stmt = select(ReportedIncidentConversation).where(
                ReportedIncidentConversation.issue_id == issue_id
            )
            result = await db.execute(stmt)
            incident_in_db = result.scalar_one()
            assert incident_in_db.created_by is None

    @pytest.mark.asyncio
    async def test_create_issue_invalid_data(
        self, client, test_agent_id, create_test_agent
    ):
        """Test issue creation with an invalid request body."""
        invalid_data = {
            "description": "This is a description",
            # Missing issue_type, state, and incidents
        }
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue", json=invalid_data
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_issue_no_incidents(
        self, client, test_agent_id, test_user_id, create_test_agent
    ):
        """Test creating an issue with no incidents."""
        issue_data = {
            "description": "A test issue with no incidents.",
            "issue_type": "GenAI",
            "state": "open",
            "reported_incidents_conversations": [],
            "reported_incidents_workflows": [],
        }
        headers = {"X-User-ID": test_user_id}
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue", json=issue_data, headers=headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["description"] == issue_data["description"]
        assert len(data["reported_incidents_conversations"]) == 0
        assert len(data["reported_incidents_workflows"]) == 0

        # Verify in DB
        async with SessionManager.get_session(db_settings) as db:
            # Convert string UUID to UUID object for database query
            issue_id = uuid.UUID(data["id"])
            issue = await db.get(Issue, issue_id)
            assert issue is not None
            await db.refresh(
                issue,
                ["reported_incidents_conversations", "reported_incidents_workflows"],
            )
            assert len(issue.reported_incidents_conversations) == 0
            assert len(issue.reported_incidents_workflows) == 0

    @pytest.mark.asyncio
    async def test_get_issue_detail_not_found(
        self, client, test_agent_id, create_test_agent
    ):
        """Test retrieving a non-existent issue."""
        non_existent_issue_id = uuid.uuid4()
        response = client.get(
            f"/agents/{test_agent_id}/core/v1/issue/{non_existent_issue_id}"
        )
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_issue_success(self, create_test_issue, client, test_agent_id):
        """Test successfully updating an issue's state and description."""
        issue = create_test_issue
        issue_id = issue.id

        update_payload = {
            "state": "closed_fixed",
            "close_desc": "Resolved by test case.",
        }

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/issue/{issue_id}",
            json=update_payload,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["state"] == "closed_fixed"
        assert data["close_desc"] == "Resolved by test case."

        # Verify in DB
        async with SessionManager.get_session() as db:
            updated_issue = await db.get(Issue, issue_id)
            assert updated_issue.state == "closed_fixed"
            assert updated_issue.close_desc == "Resolved by test case."

    @pytest.mark.asyncio
    async def test_soft_delete_incident_success(
        self, client, test_agent_id, create_test_issue, test_user_id
    ):
        """Test successfully soft-deleting an incident."""
        issue = create_test_issue
        incident_to_delete = issue.reported_incidents_conversations[0]
        incident_id = incident_to_delete.id

        delete_payload = {"is_deleted": True, "deleted_by": test_user_id}

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/incident/{incident_id}",
            json=delete_payload,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(incident_id)
        assert data["is_deleted"] is True
        assert data["deleted_by"] == test_user_id
        assert data["kind"] == "conversation"

        # Verify in DB
        async with SessionManager.get_session() as db:
            deleted_incident = await db.get(ReportedIncidentConversation, incident_id)
            assert deleted_incident.is_deleted is True
            assert deleted_incident.deleted_by == test_user_id

    @pytest.mark.asyncio
    async def test_soft_delete_incident_restore(
        self, client, test_agent_id, create_test_issue
    ):
        """Test restoring a soft-deleted incident."""
        # First, delete an incident
        issue = create_test_issue
        incident_to_modify = issue.reported_incidents_workflows[0]
        incident_id = incident_to_modify.id

        async with SessionManager.get_session() as db:
            # Use a direct query to get the object in the current session
            incident = await db.get(ReportedIncidentWorkflow, incident_id)
            incident.is_deleted = True
            incident.deleted_by = "another-user"
            await db.commit()

        # Now, restore it via the API
        restore_payload = {"is_deleted": False, "deleted_by": "restorer"}

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/incident/{incident_id}",
            json=restore_payload,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["is_deleted"] is False
        assert data["deleted_by"] is None
        assert data["kind"] == "workflow"

        # Verify in DB
        async with SessionManager.get_session() as db:
            restored_incident = await db.get(ReportedIncidentWorkflow, incident_id)
            assert restored_incident.is_deleted is False
            assert restored_incident.deleted_by is None

    @pytest.mark.asyncio
    async def test_list_agent_issues_success(
        self, client, test_agent_id, create_test_issue
    ):
        """Test successfully listing issues for a specific agent."""
        issue = create_test_issue

        response = client.get(f"/agents/{test_agent_id}/core/v1/issues?is_open=true")

        assert response.status_code == 200
        data = response.json()

        # Verify response structure
        assert "items" in data
        assert "pagination" in data
        assert "summary" in data

        # Verify we have at least one issue
        assert len(data["items"]) >= 1

        # Verify the issue data structure
        issue_item = data["items"][0]
        assert "id" in issue_item
        assert "description" in issue_item
        assert "issue_type" in issue_item
        assert "state" in issue_item
        assert "agent_name" in issue_item
        assert "incidents" in issue_item
        assert "created_at" in issue_item
        assert "updated_at" in issue_item

        # Verify pagination structure
        assert "offset" in data["pagination"]
        assert "limit" in data["pagination"]
        assert "total_items" in data["pagination"]

        # Verify summary structure (should be present for open issues)
        assert data["summary"] is not None
        assert "open" in data["summary"]
        assert "closed" in data["summary"]
        assert "by_type" in data["summary"]

    @pytest.mark.asyncio
    async def test_list_agent_issues_empty(
        self, client, test_agent_id, create_test_agent
    ):
        """Test listing issues when agent has no issues."""
        response = client.get(f"/agents/{test_agent_id}/core/v1/issues?is_open=true")

        assert response.status_code == 200
        data = response.json()
        assert data["items"] == []
        assert data["pagination"]["total_items"] == 0

    @pytest.mark.asyncio
    async def test_list_agent_issues_pagination(
        self, client, test_agent_id, create_test_issue
    ):
        """Test pagination parameters in list issues endpoint."""
        issue = create_test_issue

        response = client.get(
            f"/agents/{test_agent_id}/core/v1/issues?is_open=true&limit=1&offset=0"
        )

        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) <= 1
        assert data["pagination"]["limit"] == 1
        assert data["pagination"]["offset"] == 0

    @pytest.mark.asyncio
    async def test_get_agent_issue_states(
        self, client, test_agent_id, create_test_agent
    ):
        """Test getting possible issue states for an agent."""
        response = client.get(f"/agents/{test_agent_id}/core/v1/issue-state")

        assert response.status_code == 200
        data = response.json()

        # Verify it returns a list of strings
        assert isinstance(data, list)
        assert all(isinstance(state, str) for state in data)

        # Verify it contains expected states
        expected_states = ["open", "closed_fixed", "closed_wont_fix"]
        for state in expected_states:
            assert state in data

    @pytest.mark.asyncio
    async def test_get_agent_issue_types(
        self, client, test_agent_id, create_test_agent
    ):
        """Test getting possible issue types for an agent."""
        response = client.get(f"/agents/{test_agent_id}/core/v1/issue-types")

        assert response.status_code == 200
        data = response.json()

        # Verify it returns a list of strings
        assert isinstance(data, list)
        assert all(isinstance(issue_type, str) for issue_type in data)

        # Verify it contains expected types
        expected_types = ["Knowledge", "GenAI", "Software", "Other"]
        for issue_type in expected_types:
            assert issue_type in data

    @pytest.mark.asyncio
    async def test_add_incidents_to_existing_issue_success(
        self, client, test_agent_id, test_user_id, create_test_issue
    ):
        """Test successfully adding incidents to an existing issue."""
        issue = create_test_issue
        issue_id = issue.id

        # Prepare new incidents to add
        new_incidents_data = {
            "reported_incidents_conversations": [
                {
                    "conversation_id": str(uuid.uuid4()),
                    "answer": "New conversation incident answer",
                    "description": "New conversation incident description",
                    "severity": "low",
                }
            ],
            "reported_incidents_workflows": [
                {
                    "workflow_execution_id": str(uuid.uuid4()),
                    "step": "new_step",
                    "description": "New workflow incident description",
                    "severity": "high",
                }
            ],
        }

        headers = {"X-User-ID": test_user_id}
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue/{issue_id}/incidents",
            json=new_incidents_data,
            headers=headers,
        )

        assert response.status_code == 201
        data = response.json()

        # Verify response structure
        assert data["id"] == str(issue_id)
        assert data["agent_id"] == test_agent_id

        # Verify we now have more incidents (original + new)
        assert len(data["reported_incidents_conversations"]) == 2  # 1 original + 1 new
        assert len(data["reported_incidents_workflows"]) == 2  # 1 original + 1 new

        # Verify the new incidents are present
        conv_ids = [
            inc["conversation_id"] for inc in data["reported_incidents_conversations"]
        ]
        wf_ids = [
            inc["workflow_execution_id"] for inc in data["reported_incidents_workflows"]
        ]

        assert (
            new_incidents_data["reported_incidents_conversations"][0]["conversation_id"]
            in conv_ids
        )
        assert (
            new_incidents_data["reported_incidents_workflows"][0][
                "workflow_execution_id"
            ]
            in wf_ids
        )

        # Verify database state
        async with SessionManager.get_session() as db:
            updated_issue = await db.get(Issue, issue_id)
            await db.refresh(
                updated_issue,
                ["reported_incidents_conversations", "reported_incidents_workflows"],
            )
            assert len(updated_issue.reported_incidents_conversations) == 2
            assert len(updated_issue.reported_incidents_workflows) == 2

    @pytest.mark.asyncio
    async def test_add_incidents_to_existing_issue_empty_incidents(
        self, client, test_agent_id, test_user_id, create_test_issue
    ):
        """Test adding empty incidents list to an existing issue."""
        issue = create_test_issue
        issue_id = issue.id

        # Empty incidents payload
        empty_incidents_data = {
            "reported_incidents_conversations": [],
            "reported_incidents_workflows": [],
        }

        headers = {"X-User-ID": test_user_id}
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue/{issue_id}/incidents",
            json=empty_incidents_data,
            headers=headers,
        )

        assert response.status_code == 201
        data = response.json()

        # Should still have the original incidents
        assert len(data["reported_incidents_conversations"]) == 1
        assert len(data["reported_incidents_workflows"]) == 1

    @pytest.mark.asyncio
    async def test_add_incidents_to_nonexistent_issue(
        self, client, test_agent_id, test_user_id, create_test_agent
    ):
        """Test adding incidents to a non-existent issue."""
        non_existent_issue_id = uuid.uuid4()

        incidents_data = {
            "reported_incidents_conversations": [
                {
                    "conversation_id": str(uuid.uuid4()),
                    "answer": "Test answer",
                    "description": "Test description",
                    "severity": "medium",
                }
            ],
            "reported_incidents_workflows": [],
        }

        headers = {"X-User-ID": test_user_id}
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue/{non_existent_issue_id}/incidents",
            json=incidents_data,
            headers=headers,
        )

        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_add_incidents_missing_user_id_header(
        self, client, test_agent_id, create_test_issue
    ):
        """Test adding incidents when the X-User-ID header is missing."""
        issue = create_test_issue
        issue_id = issue.id

        incidents_data = {
            "reported_incidents_conversations": [
                {
                    "conversation_id": str(uuid.uuid4()),
                    "answer": "Test answer",
                    "description": "Test description",
                    "severity": "medium",
                }
            ],
            "reported_incidents_workflows": [],
        }

        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue/{issue_id}/incidents",
            json=incidents_data,
        )

        assert response.status_code == 201
        data = response.json()

        # Verify the new incident has None for created_by
        new_conv_incident = None
        for inc in data["reported_incidents_conversations"]:
            if (
                inc["conversation_id"]
                == incidents_data["reported_incidents_conversations"][0][
                    "conversation_id"
                ]
            ):
                new_conv_incident = inc
                break

        assert new_conv_incident is not None
        assert new_conv_incident["created_by"] is None

    @pytest.mark.asyncio
    async def test_add_incidents_invalid_data(
        self, client, test_agent_id, create_test_issue
    ):
        """Test adding incidents with invalid request body."""
        issue = create_test_issue
        issue_id = issue.id

        # Invalid data - missing required fields
        invalid_data = {
            "reported_incidents_conversations": [
                {
                    "conversation_id": str(uuid.uuid4()),
                    # Missing severity field
                    "description": "Test description",
                }
            ],
            "reported_incidents_workflows": [],
        }

        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue/{issue_id}/incidents",
            json=invalid_data,
        )

        assert response.status_code == 422
